<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Currency Alerter with Chart</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/static/css/modern-theme.css?v={% now 'U' %}">
    {% block extra_css %}{% endblock %}
    <link rel="stylesheet" href="/static/css/custom.css?v={% now 'U' %}">
</head>
<body>
    <header class="header">
        <div class="container header-container">
            <a href="{% url 'home' %}" class="logo">
                <div class="logo-icon"><i class="fab fa-bitcoin"></i></div>
                <div class="logo-text">Bitcoin<span>Alerter</span></div>
            </a>

            <!-- Desktop Navigation -->
            <nav class="desktop-nav">
                <ul class="nav">
                    <li class="nav-item"><a href="{% url 'home' %}" class="nav-link {% if request.path == '/' %}active{% endif %}"><i class="fas fa-home"></i> Home</a></li>
                    <li class="nav-item"><a href="{% url 'history' %}" class="nav-link {% if '/history/' in request.path %}active{% endif %}"><i class="fas fa-history"></i> History</a></li>
                    <li class="nav-item"><a href="{% url 'charts' %}" class="nav-link {% if '/charts/' in request.path %}active{% endif %}"><i class="fas fa-chart-line"></i> Charts</a></li>
                    <li class="nav-item"><a href="{% url 'comparison' %}" class="nav-link {% if '/comparison/' in request.path %}active{% endif %}"><i class="fas fa-balance-scale"></i> Compare</a></li>
                    <li class="nav-item"><a href="{% url 'news' %}" class="nav-link {% if '/news/' in request.path %}active{% endif %}"><i class="far fa-newspaper"></i> News</a></li>
                    <li class="nav-item"><a href="{% url 'notifications' %}" class="nav-link {% if '/notifications/' in request.path %}active{% endif %}"><i class="fas fa-bell"></i> Notifications</a></li>
                    <li class="nav-item"><a href="{% url 'converter' %}" class="nav-link {% if '/converter/' in request.path %}active{% endif %}"><i class="fas fa-exchange-alt"></i> Converter</a></li>
                    <li class="nav-item"><a href="{% url 'calculator' %}" class="nav-link {% if '/calculator/' in request.path %}active{% endif %}"><i class="fas fa-calculator"></i> Calculator</a></li>
                    <li class="nav-item"><a href="{% url 'calendar' %}" class="nav-link {% if '/calendar/' in request.path %}active{% endif %}"><i class="fas fa-calendar-alt"></i> Calendar</a></li>
                </ul>
            </nav>

            <!-- Mobile Header Controls -->
            <div class="mobile-header-controls">
                <a href="{% url 'notifications' %}" class="mobile-notification-btn">
                    <i class="fas fa-bell"></i>
                    {% if unread_notifications_count %}
                        <span class="notification-badge">{{ unread_notifications_count }}</span>
                    {% endif %}
                </a>
            </div>
        </div>
    </header>

    <!-- Enhanced Mobile Bottom Navigation Bar -->
    <nav class="mobile-bottom-nav">
        <div class="mobile-nav-container">
            <!-- Primary Navigation Items (Always Visible) -->
            <a href="{% url 'home' %}" class="mobile-nav-item {% if request.path == '/' %}active{% endif %}" title="Home" data-page="home">
                <div class="nav-icon-wrapper">
                    <i class="fas fa-home"></i>
                    <div class="nav-ripple"></div>
                </div>
                <span class="nav-label">Home</span>
            </a>

            <a href="{% url 'charts' %}" class="mobile-nav-item {% if '/charts/' in request.path %}active{% endif %}" title="Charts" data-page="charts">
                <div class="nav-icon-wrapper">
                    <i class="fas fa-chart-line"></i>
                    <div class="nav-ripple"></div>
                </div>
                <span class="nav-label">Charts</span>
            </a>

            <a href="{% url 'history' %}" class="mobile-nav-item {% if '/history/' in request.path %}active{% endif %}" title="History" data-page="history">
                <div class="nav-icon-wrapper">
                    <i class="fas fa-history"></i>
                    <div class="nav-ripple"></div>
                </div>
                <span class="nav-label">History</span>
            </a>

            <a href="{% url 'notifications' %}" class="mobile-nav-item {% if '/notifications/' in request.path %}active{% endif %}" title="Notifications" data-page="notifications">
                <div class="nav-icon-wrapper">
                    <i class="fas fa-bell"></i>
                    <div class="nav-ripple"></div>
                    {% if unread_notifications_count %}
                        <span class="mobile-nav-badge">{{ unread_notifications_count }}</span>
                    {% endif %}
                </div>
                <span class="nav-label">Alerts</span>
            </a>

            <!-- More Menu Toggle -->
            <div class="mobile-nav-more">
                <button class="mobile-nav-item mobile-more-btn" id="mobileMoreBtn" title="More Options" data-page="more">
                    <div class="nav-icon-wrapper">
                        <i class="fas fa-th-large"></i>
                        <div class="nav-ripple"></div>
                    </div>
                    <span class="nav-label">More</span>
                </button>

                <!-- Expandable More Menu -->
                <div class="mobile-more-menu" id="mobileMoreMenu">
                    <div class="mobile-more-header">
                        <h3>More Options</h3>
                        <button class="mobile-more-close" id="mobileMoreClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="mobile-more-grid">
                        <a href="{% url 'comparison' %}" class="mobile-more-item {% if '/comparison/' in request.path %}active{% endif %}">
                            <div class="more-item-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <span class="more-item-label">Compare</span>
                        </a>
                        <a href="{% url 'news' %}" class="mobile-more-item {% if '/news/' in request.path %}active{% endif %}">
                            <div class="more-item-icon">
                                <i class="far fa-newspaper"></i>
                            </div>
                            <span class="more-item-label">News</span>
                        </a>
                        <a href="{% url 'converter' %}" class="mobile-more-item {% if '/converter/' in request.path %}active{% endif %}">
                            <div class="more-item-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <span class="more-item-label">Converter</span>
                        </a>
                        <a href="{% url 'calculator' %}" class="mobile-more-item {% if '/calculator/' in request.path %}active{% endif %}">
                            <div class="more-item-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <span class="more-item-label">Calculator</span>
                        </a>
                        <a href="{% url 'calendar' %}" class="mobile-more-item {% if '/calendar/' in request.path %}active{% endif %}">
                            <div class="more-item-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <span class="more-item-label">Calendar</span>
                        </a>
                        <a href="{% url 'export_csv' %}" class="mobile-more-item">
                            <div class="more-item-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <span class="more-item-label">Export</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Overlay -->
        <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>
    </nav>

    <!-- Mobile menu functionality replaced with CSS-only solution -->

    <main class="main-content">
        <div class="container fade-in">
            <!-- Django messages section -->
            {% if messages %}
            <div class="mb-lg">
                {% for message in messages %}
                <div class="alert {% if message.tags == 'success' %}alert-success{% elif message.tags == 'error' %}alert-danger{% elif message.tags == 'warning' %}alert-warning{% else %}alert-info{% endif %} fade-in">
                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %}"></i>
                    <p>{{ message }}</p>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}
            {% endblock %}
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-left">
                    <div class="footer-logo">
                        <div class="footer-logo-icon"><i class="fab fa-bitcoin"></i></div>
                        <div class="footer-logo-text">Bitcoin<span>Alerter</span></div>
                    </div>
                    <p class="footer-description">
                        Stay updated with real-time cryptocurrency price alerts. Monitor Bitcoin, Ethereum, Dogecoin and more with customizable notifications.
                    </p>
                    <div class="footer-social">
                        <a href="#" class="footer-social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="footer-social-link"><i class="fab fa-telegram"></i></a>
                        <a href="#" class="footer-social-link"><i class="fab fa-discord"></i></a>
                        <a href="#" class="footer-social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>

                <div class="footer-right">
                    <div class="footer-links-group">
                        <h4 class="footer-links-title">Navigation</h4>
                        <div class="footer-links">
                            <a href="{% url 'home' %}" class="footer-link">Home</a>
                            <a href="{% url 'history' %}" class="footer-link">History</a>
                            <a href="{% url 'charts' %}" class="footer-link">Charts</a>
                            <a href="{% url 'news' %}" class="footer-link">News</a>
                        </div>
                    </div>

                    <div class="footer-links-group">
                        <h4 class="footer-links-title">Tools</h4>
                        <div class="footer-links">
                            <a href="{% url 'converter' %}" class="footer-link">Converter</a>
                            <a href="{% url 'calculator' %}" class="footer-link">Calculator</a>
                            <a href="{% url 'export_csv' %}" class="footer-link">Export Data</a>
                        </div>
                    </div>

                    <div class="footer-links-group">
                        <h4 class="footer-links-title">Resources</h4>
                        <div class="footer-links">
                            <a href="#" class="footer-link">API Documentation</a>
                            <a href="#" class="footer-link">Help Center</a>
                            <a href="#" class="footer-link">Contact Us</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">&copy; 2025 Bitcoin Currency Alerter with Chart. All rights reserved.</div>
                <div class="footer-bottom-links">
                    <a href="#" class="footer-bottom-link">Privacy Policy</a>
                    <a href="#" class="footer-bottom-link">Terms of Service</a>
                    <a href="#" class="footer-bottom-link">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Enhanced Mobile Bottom Navigation JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced Mobile Navigation Functionality
            const moreBtn = document.getElementById('mobileMoreBtn');
            const moreMenu = document.getElementById('mobileMoreMenu');
            const moreClose = document.getElementById('mobileMoreClose');
            const navOverlay = document.getElementById('mobileNavOverlay');

            // Open more menu
            if (moreBtn && moreMenu) {
                moreBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    openMoreMenu();
                });
            }

            // Close more menu
            if (moreClose) {
                moreClose.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeMoreMenu();
                });
            }

            // Close menu when clicking overlay
            if (navOverlay) {
                navOverlay.addEventListener('click', function() {
                    closeMoreMenu();
                });
            }

            // Close menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeMoreMenu();
                }
            });

            function openMoreMenu() {
                moreMenu.classList.add('active');
                moreBtn.classList.add('active');
                navOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Add stagger animation to menu items
                const menuItems = moreMenu.querySelectorAll('.mobile-more-item');
                menuItems.forEach((item, index) => {
                    item.style.animationDelay = `${index * 0.1}s`;
                    item.classList.add('animate-in');
                });
            }

            function closeMoreMenu() {
                moreMenu.classList.remove('active');
                moreBtn.classList.remove('active');
                navOverlay.classList.remove('active');
                document.body.style.overflow = '';

                // Remove animation classes
                const menuItems = moreMenu.querySelectorAll('.mobile-more-item');
                menuItems.forEach(item => {
                    item.classList.remove('animate-in');
                    item.style.animationDelay = '';
                });
            }

            // Enhanced ripple effect for mobile nav items
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Create ripple effect
                    const rippleWrapper = this.querySelector('.nav-ripple');
                    if (rippleWrapper) {
                        const rect = this.getBoundingClientRect();
                        const size = Math.max(rect.width, rect.height);
                        const x = e.clientX - rect.left - size / 2;
                        const y = e.clientY - rect.top - size / 2;

                        rippleWrapper.style.width = size + 'px';
                        rippleWrapper.style.height = size + 'px';
                        rippleWrapper.style.left = x + 'px';
                        rippleWrapper.style.top = y + 'px';
                        rippleWrapper.classList.add('active');

                        setTimeout(() => {
                            rippleWrapper.classList.remove('active');
                        }, 600);
                    }

                    // Add haptic feedback simulation
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                });
            });

            // Handle mobile nav badge animations
            const navBadges = document.querySelectorAll('.mobile-nav-badge');
            navBadges.forEach(badge => {
                badge.style.animation = 'badgePulse 2s infinite';
            });

            // Close more menu when clicking on a link
            const moreLinks = document.querySelectorAll('.mobile-more-item');
            moreLinks.forEach(link => {
                link.addEventListener('click', function() {
                    closeMoreMenu();
                });
            });

            // Auto-hide navigation on scroll (optional)
            let lastScrollTop = 0;
            const mobileNav = document.querySelector('.mobile-bottom-nav');

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down
                    mobileNav.classList.add('nav-hidden');
                } else {
                    // Scrolling up
                    mobileNav.classList.remove('nav-hidden');
                }

                lastScrollTop = scrollTop;
            }, { passive: true });
        });
    </script>
</body>
</html>