<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Currency Alerter with Chart</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/static/css/modern-theme.css?v={% now 'U' %}">
    {% block extra_css %}{% endblock %}
    <link rel="stylesheet" href="/static/css/custom.css?v={% now 'U' %}">
</head>
<body>
    <header class="header">
        <div class="container header-container">
            <a href="{% url 'home' %}" class="logo">
                <div class="logo-icon"><i class="fab fa-bitcoin"></i></div>
                <div class="logo-text">Bitcoin<span>Alerter</span></div>
            </a>

            <!-- Desktop Navigation -->
            <nav class="desktop-nav">
                <ul class="nav">
                    <li class="nav-item"><a href="{% url 'home' %}" class="nav-link {% if request.path == '/' %}active{% endif %}"><i class="fas fa-home"></i> Home</a></li>
                    <li class="nav-item"><a href="{% url 'history' %}" class="nav-link {% if '/history/' in request.path %}active{% endif %}"><i class="fas fa-history"></i> History</a></li>
                    <li class="nav-item"><a href="{% url 'charts' %}" class="nav-link {% if '/charts/' in request.path %}active{% endif %}"><i class="fas fa-chart-line"></i> Charts</a></li>
                    <li class="nav-item"><a href="{% url 'comparison' %}" class="nav-link {% if '/comparison/' in request.path %}active{% endif %}"><i class="fas fa-balance-scale"></i> Compare</a></li>
                    <li class="nav-item"><a href="{% url 'news' %}" class="nav-link {% if '/news/' in request.path %}active{% endif %}"><i class="far fa-newspaper"></i> News</a></li>
                    <li class="nav-item"><a href="{% url 'notifications' %}" class="nav-link {% if '/notifications/' in request.path %}active{% endif %}"><i class="fas fa-bell"></i> Notifications</a></li>
                    <li class="nav-item"><a href="{% url 'converter' %}" class="nav-link {% if '/converter/' in request.path %}active{% endif %}"><i class="fas fa-exchange-alt"></i> Converter</a></li>
                    <li class="nav-item"><a href="{% url 'calculator' %}" class="nav-link {% if '/calculator/' in request.path %}active{% endif %}"><i class="fas fa-calculator"></i> Calculator</a></li>
                    <li class="nav-item"><a href="{% url 'calendar' %}" class="nav-link {% if '/calendar/' in request.path %}active{% endif %}"><i class="fas fa-calendar-alt"></i> Calendar</a></li>
                </ul>
            </nav>

            <!-- Mobile Header Controls -->
            <div class="mobile-header-controls">
                <a href="{% url 'notifications' %}" class="mobile-notification-btn">
                    <i class="fas fa-bell"></i>
                    {% if unread_notifications_count %}
                        <span class="notification-badge">{{ unread_notifications_count }}</span>
                    {% endif %}
                </a>
            </div>
        </div>
    </header>

    <!-- Mobile Bottom Navigation Bar -->
    <nav class="mobile-bottom-nav">
        <div class="mobile-nav-container">
            <a href="{% url 'home' %}" class="mobile-nav-item {% if request.path == '/' %}active{% endif %}" title="Home">
                <i class="fas fa-home"></i>
                <span class="nav-label">Home</span>
            </a>
            <a href="{% url 'history' %}" class="mobile-nav-item {% if '/history/' in request.path %}active{% endif %}" title="History">
                <i class="fas fa-history"></i>
                <span class="nav-label">History</span>
            </a>
            <a href="{% url 'charts' %}" class="mobile-nav-item {% if '/charts/' in request.path %}active{% endif %}" title="Charts">
                <i class="fas fa-chart-line"></i>
                <span class="nav-label">Charts</span>
            </a>
            <a href="{% url 'notifications' %}" class="mobile-nav-item {% if '/notifications/' in request.path %}active{% endif %}" title="Notifications">
                <i class="fas fa-bell"></i>
                <span class="nav-label">Alerts</span>
                {% if unread_notifications_count %}
                    <span class="mobile-nav-badge">{{ unread_notifications_count }}</span>
                {% endif %}
            </a>
            <div class="mobile-nav-more">
                <button class="mobile-nav-item mobile-more-btn" id="mobileMoreBtn" title="More">
                    <i class="fas fa-ellipsis-h"></i>
                    <span class="nav-label">More</span>
                </button>
                <div class="mobile-more-menu" id="mobileMoreMenu">
                    <a href="{% url 'comparison' %}" class="mobile-more-item {% if '/comparison/' in request.path %}active{% endif %}">
                        <i class="fas fa-balance-scale"></i>
                        <span>Compare</span>
                    </a>
                    <a href="{% url 'news' %}" class="mobile-more-item {% if '/news/' in request.path %}active{% endif %}">
                        <i class="far fa-newspaper"></i>
                        <span>News</span>
                    </a>
                    <a href="{% url 'converter' %}" class="mobile-more-item {% if '/converter/' in request.path %}active{% endif %}">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Converter</span>
                    </a>
                    <a href="{% url 'calculator' %}" class="mobile-more-item {% if '/calculator/' in request.path %}active{% endif %}">
                        <i class="fas fa-calculator"></i>
                        <span>Calculator</span>
                    </a>
                    <a href="{% url 'calendar' %}" class="mobile-more-item {% if '/calendar/' in request.path %}active{% endif %}">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Bitcoin Calendar</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile menu functionality replaced with CSS-only solution -->

    <main class="main-content">
        <div class="container fade-in">
            <!-- Django messages section -->
            {% if messages %}
            <div class="mb-lg">
                {% for message in messages %}
                <div class="alert {% if message.tags == 'success' %}alert-success{% elif message.tags == 'error' %}alert-danger{% elif message.tags == 'warning' %}alert-warning{% else %}alert-info{% endif %} fade-in">
                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %}"></i>
                    <p>{{ message }}</p>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}
            {% endblock %}
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-left">
                    <div class="footer-logo">
                        <div class="footer-logo-icon"><i class="fab fa-bitcoin"></i></div>
                        <div class="footer-logo-text">Bitcoin<span>Alerter</span></div>
                    </div>
                    <p class="footer-description">
                        Stay updated with real-time cryptocurrency price alerts. Monitor Bitcoin, Ethereum, Dogecoin and more with customizable notifications.
                    </p>
                    <div class="footer-social">
                        <a href="#" class="footer-social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="footer-social-link"><i class="fab fa-telegram"></i></a>
                        <a href="#" class="footer-social-link"><i class="fab fa-discord"></i></a>
                        <a href="#" class="footer-social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>

                <div class="footer-right">
                    <div class="footer-links-group">
                        <h4 class="footer-links-title">Navigation</h4>
                        <div class="footer-links">
                            <a href="{% url 'home' %}" class="footer-link">Home</a>
                            <a href="{% url 'history' %}" class="footer-link">History</a>
                            <a href="{% url 'charts' %}" class="footer-link">Charts</a>
                            <a href="{% url 'news' %}" class="footer-link">News</a>
                        </div>
                    </div>

                    <div class="footer-links-group">
                        <h4 class="footer-links-title">Tools</h4>
                        <div class="footer-links">
                            <a href="{% url 'converter' %}" class="footer-link">Converter</a>
                            <a href="{% url 'calculator' %}" class="footer-link">Calculator</a>
                            <a href="{% url 'export_csv' %}" class="footer-link">Export Data</a>
                        </div>
                    </div>

                    <div class="footer-links-group">
                        <h4 class="footer-links-title">Resources</h4>
                        <div class="footer-links">
                            <a href="#" class="footer-link">API Documentation</a>
                            <a href="#" class="footer-link">Help Center</a>
                            <a href="#" class="footer-link">Contact Us</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">&copy; 2025 Bitcoin Currency Alerter with Chart. All rights reserved.</div>
                <div class="footer-bottom-links">
                    <a href="#" class="footer-bottom-link">Privacy Policy</a>
                    <a href="#" class="footer-bottom-link">Terms of Service</a>
                    <a href="#" class="footer-bottom-link">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Mobile Bottom Navigation JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile "More" menu functionality
            const mobileMoreBtn = document.getElementById('mobileMoreBtn');
            const mobileMoreMenu = document.getElementById('mobileMoreMenu');

            if (mobileMoreBtn && mobileMoreMenu) {
                // Toggle more menu
                mobileMoreBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    mobileMoreMenu.classList.toggle('active');
                });

                // Close more menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!mobileMoreBtn.contains(e.target) && !mobileMoreMenu.contains(e.target)) {
                        mobileMoreMenu.classList.remove('active');
                    }
                });

                // Close more menu when clicking on a link
                const moreLinks = mobileMoreMenu.querySelectorAll('.mobile-more-item');
                moreLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMoreMenu.classList.remove('active');
                    });
                });
            }

            // Add active state animation for mobile nav items
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Add ripple effect
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Handle mobile nav badge animations
            const navBadges = document.querySelectorAll('.mobile-nav-badge');
            navBadges.forEach(badge => {
                badge.style.animation = 'badgePulse 2s infinite';
            });
        });
    </script>
</body>
</html>