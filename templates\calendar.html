{% extends 'base.html' %}

{% block content %}
<div class="dashboard-header">
    <h1 class="page-title" data-text="Bitcoin Calendar">Bitcoin Calendar</h1>
    <div class="dashboard-actions">
        <a href="{% url 'home' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> New Alert
        </a>
        <button class="btn btn-outline" onclick="window.location.reload()">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
    </div>
</div>

<!-- Calendar Navigation -->
<div class="calendar-nav">
    <div class="calendar-nav-controls">
        <button class="calendar-nav-btn" id="prevMonth">
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="calendar-current-month" id="currentMonth">
            <span class="month-name">January</span>
            <span class="year">2025</span>
        </div>
        <button class="calendar-nav-btn" id="nextMonth">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    <div class="calendar-view-toggle">
        <button class="view-toggle-btn active" data-view="month">Month</button>
        <button class="view-toggle-btn" data-view="week">Week</button>
    </div>
</div>

<!-- Bitcoin Price Summary for Current Month -->
<div class="calendar-summary">
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fab fa-bitcoin"></i>
        </div>
        <div class="summary-content">
            <h3>Monthly High</h3>
            <p class="summary-value">$45,230.50</p>
            <span class="summary-date">Jan 15, 2025</span>
        </div>
    </div>
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="summary-content">
            <h3>Monthly Low</h3>
            <p class="summary-value">$38,450.20</p>
            <span class="summary-date">Jan 3, 2025</span>
        </div>
    </div>
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-percentage"></i>
        </div>
        <div class="summary-content">
            <h3>Monthly Change</h3>
            <p class="summary-value positive">+8.5%</p>
            <span class="summary-date">This month</span>
        </div>
    </div>
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-bell"></i>
        </div>
        <div class="summary-content">
            <h3>Alerts Triggered</h3>
            <p class="summary-value">{{ alerts|length }}</p>
            <span class="summary-date">This month</span>
        </div>
    </div>
</div>

<!-- Bitcoin Calendar Grid -->
<div class="calendar-container">
    <div class="calendar-grid" id="calendarGrid">
        <!-- Calendar Header -->
        <div class="calendar-header">
            <div class="calendar-day-header">Sun</div>
            <div class="calendar-day-header">Mon</div>
            <div class="calendar-day-header">Tue</div>
            <div class="calendar-day-header">Wed</div>
            <div class="calendar-day-header">Thu</div>
            <div class="calendar-day-header">Fri</div>
            <div class="calendar-day-header">Sat</div>
        </div>
        
        <!-- Calendar Days -->
        <div class="calendar-days" id="calendarDays">
            <!-- Days will be generated by JavaScript -->
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="calendar-modal" id="eventModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Bitcoin Events</h3>
            <button class="modal-close" id="modalClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="modalBody">
            <!-- Event details will be populated here -->
        </div>
    </div>
</div>

<!-- Calendar Legend -->
<div class="calendar-legend">
    <h3>Legend</h3>
    <div class="legend-items">
        <div class="legend-item">
            <div class="legend-color price-high"></div>
            <span>Price High Day</span>
        </div>
        <div class="legend-item">
            <div class="legend-color price-low"></div>
            <span>Price Low Day</span>
        </div>
        <div class="legend-item">
            <div class="legend-color alert-triggered"></div>
            <span>Alert Triggered</span>
        </div>
        <div class="legend-item">
            <div class="legend-color significant-move"></div>
            <span>Significant Move (>5%)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color today"></div>
            <span>Today</span>
        </div>
    </div>
</div>

<!-- Calendar Features -->
<div class="card card-primary">
    <div class="card-header">
        <h2 class="card-title">Bitcoin Calendar Features</h2>
        <p class="card-subtitle">Track important Bitcoin events and price movements</p>
    </div>
    <div class="card-body">
        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <h3 class="feature-title">Daily Price Tracking</h3>
                <p class="feature-description">View Bitcoin price movements for each day with visual indicators for highs, lows, and significant changes.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <h3 class="feature-title">Alert History</h3>
                <p class="feature-description">See which days your price alerts were triggered with detailed information about each event.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">Monthly Trends</h3>
                <p class="feature-description">Analyze monthly Bitcoin performance with summary statistics and trend indicators.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h3 class="feature-title">Event Details</h3>
                <p class="feature-description">Click on any day to view detailed information about Bitcoin events, price changes, and market activity.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calendar functionality
    const currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();
    
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    // Sample Bitcoin events data
    const bitcoinEvents = {
        '2025-01-15': {
            price: 45230.50,
            change: 8.5,
            events: ['Monthly High', 'Strong buying pressure'],
            alerts: 2
        },
        '2025-01-03': {
            price: 38450.20,
            change: -12.3,
            events: ['Monthly Low', 'Market correction'],
            alerts: 1
        },
        '2025-01-10': {
            price: 42100.00,
            change: 5.8,
            events: ['Significant move up'],
            alerts: 1
        }
    };
    
    function generateCalendar(month, year) {
        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        const calendarDays = document.getElementById('calendarDays');
        const currentMonthElement = document.getElementById('currentMonth');
        
        // Update month/year display
        currentMonthElement.innerHTML = `
            <span class="month-name">${monthNames[month]}</span>
            <span class="year">${year}</span>
        `;
        
        // Clear previous calendar
        calendarDays.innerHTML = '';
        
        // Add empty cells for days before month starts
        for (let i = 0; i < firstDay; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day empty';
            calendarDays.appendChild(emptyDay);
        }
        
        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            
            const dateKey = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const eventData = bitcoinEvents[dateKey];
            
            // Check if it's today
            const today = new Date();
            if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
                dayElement.classList.add('today');
            }
            
            // Add event indicators
            if (eventData) {
                if (eventData.events.includes('Monthly High')) {
                    dayElement.classList.add('price-high');
                } else if (eventData.events.includes('Monthly Low')) {
                    dayElement.classList.add('price-low');
                } else if (Math.abs(eventData.change) > 5) {
                    dayElement.classList.add('significant-move');
                }
                
                if (eventData.alerts > 0) {
                    dayElement.classList.add('alert-triggered');
                }
            }
            
            dayElement.innerHTML = `
                <span class="day-number">${day}</span>
                ${eventData ? `<div class="day-indicators">
                    ${eventData.alerts > 0 ? '<i class="fas fa-bell"></i>' : ''}
                    ${Math.abs(eventData.change) > 5 ? '<i class="fas fa-chart-line"></i>' : ''}
                </div>` : ''}
            `;
            
            // Add click event
            dayElement.addEventListener('click', () => showEventDetails(dateKey, eventData));
            
            calendarDays.appendChild(dayElement);
        }
    }
    
    function showEventDetails(dateKey, eventData) {
        const modal = document.getElementById('eventModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        
        const date = new Date(dateKey);
        const formattedDate = date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        
        modalTitle.textContent = formattedDate;
        
        if (eventData) {
            modalBody.innerHTML = `
                <div class="event-details">
                    <div class="price-info">
                        <h4>Bitcoin Price</h4>
                        <p class="price-value">$${eventData.price.toLocaleString()}</p>
                        <p class="price-change ${eventData.change > 0 ? 'positive' : 'negative'}">
                            ${eventData.change > 0 ? '+' : ''}${eventData.change}%
                        </p>
                    </div>
                    <div class="events-info">
                        <h4>Events</h4>
                        <ul>
                            ${eventData.events.map(event => `<li>${event}</li>`).join('')}
                        </ul>
                    </div>
                    ${eventData.alerts > 0 ? `
                        <div class="alerts-info">
                            <h4>Alerts Triggered</h4>
                            <p>${eventData.alerts} alert${eventData.alerts > 1 ? 's' : ''} triggered on this day</p>
                        </div>
                    ` : ''}
                </div>
            `;
        } else {
            modalBody.innerHTML = `
                <div class="no-events">
                    <i class="fas fa-calendar-day"></i>
                    <p>No Bitcoin events recorded for this day.</p>
                </div>
            `;
        }
        
        modal.style.display = 'flex';
    }
    
    // Navigation event listeners
    document.getElementById('prevMonth').addEventListener('click', () => {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        generateCalendar(currentMonth, currentYear);
    });
    
    document.getElementById('nextMonth').addEventListener('click', () => {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        generateCalendar(currentMonth, currentYear);
    });
    
    // Modal close event
    document.getElementById('modalClose').addEventListener('click', () => {
        document.getElementById('eventModal').style.display = 'none';
    });
    
    // Close modal when clicking outside
    document.getElementById('eventModal').addEventListener('click', (e) => {
        if (e.target.id === 'eventModal') {
            document.getElementById('eventModal').style.display = 'none';
        }
    });
    
    // Initialize calendar
    generateCalendar(currentMonth, currentYear);
});
</script>
{% endblock %}
