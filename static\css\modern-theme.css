/* Modern Theme for Bitcoin Currency Alerter */

/* ===== VARIABLES ===== */
:root {
    /* Color Palette - Enhanced Dark Theme */
    --primary-darkest: #0a0f1a;
    --primary-dark: #0f172a;
    --primary: #1e293b;
    --primary-light: #334155;
    --primary-lighter: #475569;

    --secondary-darkest: #4c1d95;
    --secondary-dark: #581c87;
    --secondary: #7e22ce;
    --secondary-light: #a855f7;
    --secondary-lighter: #c084fc;

    --success-darkest: #064e3b;
    --success-dark: #065f46;
    --success: #10b981;
    --success-light: #34d399;
    --success-lighter: #6ee7b7;

    --danger-darkest: #7f1d1d;
    --danger-dark: #991b1b;
    --danger: #dc2626;
    --danger-light: #ef4444;
    --danger-lighter: #f87171;

    --warning-darkest: #78350f;
    --warning-dark: #92400e;
    --warning: #f59e0b;
    --warning-light: #fbbf24;
    --warning-lighter: #fcd34d;

    --info-darkest: #0c4a6e;
    --info-dark: #075985;
    --info: #0ea5e9;
    --info-light: #38bdf8;
    --info-lighter: #7dd3fc;

    /* Cryptocurrency Colors */
    --bitcoin: #f7931a;
    --bitcoin-dark: #d97706;
    --bitcoin-darker: #b45309;
    --bitcoin-light: #fbbf24;
    --bitcoin-lighter: #fcd34d;
    --bitcoin-glow: rgba(247, 147, 26, 0.5);

    --ethereum: #627eea;
    --ethereum-dark: #4f46e5;
    --ethereum-darker: #4338ca;
    --ethereum-light: #818cf8;
    --ethereum-lighter: #a5b4fc;
    --ethereum-glow: rgba(98, 126, 234, 0.5);

    --dogecoin: #c3a634;
    --dogecoin-dark: #a16207;
    --dogecoin-darker: #854d0e;
    --dogecoin-light: #eab308;
    --dogecoin-lighter: #facc15;
    --dogecoin-glow: rgba(195, 166, 52, 0.5);

    /* Background Colors */
    --bg-darkest: #030712;
    --bg-dark: #0f172a;
    --bg-medium: #1e293b;
    --bg-light: #334155;
    --bg-lighter: #475569;
    --bg-lightest: #64748b;

    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-dim: #64748b;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-dark) 0%, var(--secondary) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-dark) 0%, var(--success) 100%);
    --gradient-danger: linear-gradient(135deg, var(--danger-dark) 0%, var(--danger) 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-dark) 0%, var(--warning) 100%);
    --gradient-info: linear-gradient(135deg, var(--info-dark) 0%, var(--info) 100%);
    --gradient-dark: linear-gradient(135deg, var(--bg-darkest) 0%, var(--bg-dark) 100%);
    --gradient-bitcoin: linear-gradient(135deg, var(--bitcoin-darker) 0%, var(--bitcoin) 100%);
    --gradient-ethereum: linear-gradient(135deg, var(--ethereum-darker) 0%, var(--ethereum) 100%);
    --gradient-dogecoin: linear-gradient(135deg, var(--dogecoin-darker) 0%, var(--dogecoin) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0.03) 100%);
    --gradient-glass-dark: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
    --gradient-radial: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    --gradient-conic: conic-gradient(from 225deg, var(--bitcoin) 0%, var(--ethereum) 50%, var(--dogecoin) 100%);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.2), 0 3px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 12px 24px rgba(0, 0, 0, 0.25), 0 8px 12px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 18px 36px rgba(0, 0, 0, 0.3), 0 14px 20px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.35), 0 20px 30px rgba(0, 0, 0, 0.3);
    --shadow-inner: inset 0 2px 6px rgba(0, 0, 0, 0.2);
    --shadow-bitcoin: 0 0 20px var(--bitcoin-glow);
    --shadow-ethereum: 0 0 20px var(--ethereum-glow);
    --shadow-dogecoin: 0 0 20px var(--dogecoin-glow);
    --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.15);
    --shadow-button: 0 4px 10px rgba(0, 0, 0, 0.2), 0 2px 5px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 14px 28px rgba(0, 0, 0, 0.3), 0 10px 10px rgba(0, 0, 0, 0.2);

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-round: 50%;
    --radius-full: 9999px;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 2.5rem;
    --space-3xl: 3rem;
    --space-4xl: 4rem;

    /* Transitions - Enhanced for better UX */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* Breakpoints */
    --breakpoint-xs: 480px;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;

    /* Z-index */
    --z-negative: -1;
    --z-normal: 1;
    --z-tooltip: 10;
    --z-fixed: 100;
    --z-modal: 1000;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
}

/* ===== BASE STYLES ===== */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-darkest);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(247, 147, 26, 0.05) 0%, transparent 35%),
        radial-gradient(circle at 90% 80%, rgba(98, 126, 234, 0.05) 0%, transparent 35%),
        radial-gradient(circle at 50% 50%, rgba(195, 166, 52, 0.03) 0%, transparent 70%),
        linear-gradient(135deg, var(--bg-darkest) 0%, var(--bg-dark) 100%);
    background-attachment: fixed;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(to right, rgba(247, 147, 26, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(247, 147, 26, 0.1) 1px, transparent 1px);
    background-size: 40px 40px;
    opacity: 0.03;
    z-index: var(--z-negative);
    pointer-events: none;
    animation: gridFade 10s infinite alternate;
}

@keyframes gridFade {
    0% {
        opacity: 0.02;
        background-size: 40px 40px;
    }
    100% {
        opacity: 0.04;
        background-size: 42px 42px;
    }
}

a {
    color: var(--bitcoin);
    text-decoration: none;
    transition: var(--transition-normal);
    position: relative;
}

a:hover {
    color: var(--bitcoin-light);
}

a:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(247, 147, 26, 0.3);
}

button {
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
}

button:focus {
    outline: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* ===== LAYOUT ===== */
.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

@media (max-width: 576px) {
    .container {
        padding: 0 var(--space-md);
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

.main-content {
    padding-top: var(--space-2xl);
    padding-bottom: var(--space-4xl);
    position: relative;
    z-index: var(--z-normal);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-medium);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--bitcoin);
}

/* ===== HEADER ===== */
.header {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: var(--space-md) 0;
    position: sticky;
    top: 0;
    z-index: var(--z-fixed);
    border-bottom: 1px solid rgba(255, 149, 0, 0.1);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.header.scrolled {
    padding: var(--space-sm) 0;
    background: rgba(15, 23, 42, 0.98);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    height: 60px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: auto;
    margin-right: 24px;
}

/* Theme Toggle */
.theme-toggle {
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.2);
    color: #f7931a;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    position: relative;
    overflow: hidden;
}

.theme-toggle:hover {
    background-color: rgba(247, 147, 26, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(247, 147, 26, 0.2);
}

.theme-toggle:active {
    transform: translateY(0);
}

.theme-toggle::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.theme-toggle:hover::after {
    opacity: 1;
}

/* Theme Transitions */
.theme-transition,
.theme-transition *:not(script):not(style) {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.rotate-animation {
    animation: rotate 0.5s ease-in-out;
}

/* Light Theme */
body.light-theme {
    background: #f8fafc;
    color: #334155;
}

body.light-theme .header {
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

body.light-theme .header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

body.light-theme .theme-toggle {
    background: rgba(247, 147, 26, 0.1);
    color: #f7931a;
}

body.light-theme .card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

body.light-theme .card::before {
    opacity: 0.7;
}

body.light-theme .card-header {
    border-bottom: 1px solid #f1f5f9;
}

body.light-theme .card-title {
    color: #1e293b;
}

body.light-theme .card-subtitle {
    color: #64748b;
}

body.light-theme .table {
    background: #ffffff;
}

body.light-theme .table th {
    background: #f8fafc;
    color: #1e293b;
    border-bottom: 2px solid #e2e8f0;
}

body.light-theme .table td {
    border-bottom: 1px solid #e2e8f0;
    color: #334155;
}

body.light-theme .table tr:hover td {
    background: #f1f5f9;
}

body.light-theme .btn-primary {
    background: #f7931a;
    color: #ffffff;
    border: none;
}

body.light-theme .btn-secondary {
    background: #f1f5f9;
    color: #334155;
    border: 1px solid #e2e8f0;
}

body.light-theme .btn-outline {
    border: 1px solid #e2e8f0;
    color: #64748b;
}

body.light-theme .nav-link {
    color: #64748b;
}

body.light-theme .nav-link:hover {
    color: #1e293b;
}

body.light-theme .nav-link.active {
    color: #f7931a;
    background: rgba(247, 147, 26, 0.1);
}

body.light-theme .form-control {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    color: #334155;
}

body.light-theme .form-control:focus {
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.2);
}

body.light-theme .form-label {
    color: #475569;
}

body.light-theme .price-card {
    background: #ffffff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

body.light-theme .price-value {
    color: #1e293b;
}

body.light-theme .notification-bell i {
    color: #64748b;
}

body.light-theme .notification-bell:hover i {
    color: #1e293b;
}

body.light-theme .notification-dropdown {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

body.light-theme .notification-header {
    border-bottom: 1px solid #e2e8f0;
}

body.light-theme .notification-header h3 {
    color: #1e293b;
}

body.light-theme .notification-item {
    border-bottom: 1px solid #f1f5f9;
}

body.light-theme .notification-item:hover {
    background: #f8fafc;
}

body.light-theme .notification-title {
    color: #1e293b;
}

body.light-theme .notification-message {
    color: #64748b;
}

body.light-theme .notification-time {
    color: #94a3b8;
}

/* Enhanced Notification Bell */
.notification-bell {
    position: relative;
    cursor: pointer;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    transition: var(--transition-normal);
    overflow: hidden;
    text-decoration: none;
    border: 1px solid rgba(247, 147, 26, 0.1);
    box-shadow: var(--shadow-sm);
}

.notification-bell::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(247, 147, 26, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-slow);
}

.notification-bell:hover {
    background: rgba(247, 147, 26, 0.1);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-bitcoin);
    border-color: rgba(247, 147, 26, 0.3);
}

.notification-bell:hover::before {
    opacity: 1;
}

.notification-bell:active {
    transform: translateY(-1px) scale(0.98);
}

.notification-bell:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.notification-bell i {
    font-size: 1.3rem;
    color: #a0aec0;
    transition: var(--transition-normal);
    z-index: 2;
    position: relative;
}

.notification-bell:hover i {
    color: #f7931a;
    transform: scale(1.15) rotate(15deg);
}

.notification-bell.active i {
    color: #f7931a;
    transform: scale(1.1);
}

/* Enhanced notification bell animation for new notifications */
.notification-bell.has-new {
    animation: bellShake 0.6s ease-in-out;
}

@keyframes bellShake {
    0%, 100% { transform: rotate(0deg); }
    10% { transform: rotate(-15deg); }
    20% { transform: rotate(10deg); }
    30% { transform: rotate(-10deg); }
    40% { transform: rotate(5deg); }
    50% { transform: rotate(-5deg); }
    60% { transform: rotate(3deg); }
    70% { transform: rotate(-3deg); }
    80% { transform: rotate(1deg); }
    90% { transform: rotate(-1deg); }
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 700;
    min-width: 22px;
    height: 22px;
    border-radius: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    border: 2px solid var(--bg-dark);
    animation: notificationBadgePulse 2s infinite;
    z-index: 10;
    padding: 0 6px;
}

@keyframes notificationBadgePulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* Responsive notification bell */
@media (max-width: 768px) {
    .notification-bell {
        width: 40px;
        height: 40px;
    }

    .notification-bell i {
        font-size: 1.2rem;
    }

    .notification-badge {
        top: -6px;
        right: -6px;
        min-width: 20px;
        height: 20px;
        font-size: 0.65rem;
    }
}

@media (max-width: 480px) {
    .notification-bell {
        width: 36px;
        height: 36px;
    }

    .notification-bell i {
        font-size: 1.1rem;
    }

    .notification-badge {
        top: -5px;
        right: -5px;
        min-width: 18px;
        height: 18px;
        font-size: 0.6rem;
    }
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: -100px;
    width: 350px;
    background: #1a202c;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(74, 85, 104, 0.8);
    z-index: 1000;
    display: none;
    overflow: hidden;
    margin-top: 12px;
    backdrop-filter: blur(10px);
    transform: translateY(10px);
    opacity: 0;
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.3s ease;
}

.notification-dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 110px;
    width: 12px;
    height: 12px;
    background: #1a202c;
    transform: rotate(45deg);
    border-left: 1px solid rgba(74, 85, 104, 0.8);
    border-top: 1px solid rgba(74, 85, 104, 0.8);
    z-index: 1001;
}

.notification-bell.active .notification-dropdown {
    display: block;
    transform: translateY(0);
    opacity: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.5);
    background: linear-gradient(to right, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
}

.notification-header-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Enhanced Notification Settings Button */
.notification-settings {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-tertiary);
    font-size: 0.85rem;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.notification-settings::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.notification-settings:hover {
    background: rgba(247, 147, 26, 0.15);
    color: var(--bitcoin);
    border-color: rgba(247, 147, 26, 0.3);
    transform: rotate(90deg) scale(1.05);
    box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3);
}

.notification-settings:hover::before {
    opacity: 1;
}

.notification-settings:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.notification-settings:active {
    transform: rotate(90deg) scale(0.95);
}

.notification-header h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Enhanced Notification Clear Button */
.notification-clear {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-tertiary);
    font-size: 0.85rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 36px;
}

.notification-clear::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.notification-clear:hover {
    background: rgba(247, 147, 26, 0.15);
    color: var(--bitcoin);
    border-color: rgba(247, 147, 26, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3);
}

.notification-clear:hover::before {
    opacity: 1;
}

.notification-clear:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.notification-clear:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

/* Notification Filters */
.notification-filters {
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
    background: rgba(45, 55, 72, 0.3);
    overflow-x: auto;
    scrollbar-width: none;
}

.notification-filters::-webkit-scrollbar {
    display: none;
}

/* Enhanced Notification Filter Buttons */
.notification-filter {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-tertiary);
    font-size: 0.8rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
    white-space: nowrap;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.notification-filter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.notification-filter:hover {
    background: rgba(247, 147, 26, 0.15);
    color: var(--bitcoin);
    border-color: rgba(247, 147, 26, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3);
}

.notification-filter:hover::before {
    opacity: 1;
}

.notification-filter:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.notification-filter:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.notification-filter.active {
    background: rgba(247, 147, 26, 0.2);
    color: var(--bitcoin);
    border-color: rgba(247, 147, 26, 0.5);
    box-shadow: 0 4px 15px rgba(247, 147, 26, 0.4);
    transform: translateY(-1px);
}

.notification-filter.active::before {
    opacity: 1;
}

/* Notification Footer */
.notification-footer {
    padding: 12px 20px;
    border-top: 1px solid rgba(74, 85, 104, 0.3);
    background: rgba(45, 55, 72, 0.3);
}

/* Enhanced Notification View All Button */
.notification-view-all {
    width: 100%;
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.3);
    color: var(--bitcoin);
    font-size: 0.9rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.notification-view-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.notification-view-all:hover {
    background: rgba(247, 147, 26, 0.2);
    border-color: rgba(247, 147, 26, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(247, 147, 26, 0.4);
}

.notification-view-all:hover::before {
    opacity: 1;
}

.notification-view-all:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.notification-view-all:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px 0;
    scrollbar-width: thin;
    scrollbar-color: #4a5568 #2d3748;
}

.notification-list::-webkit-scrollbar {
    width: 6px;
}

.notification-list::-webkit-scrollbar-track {
    background: #2d3748;
}

.notification-list::-webkit-scrollbar-thumb {
    background-color: #4a5568;
    border-radius: 6px;
}

/* Enhanced Notification Items */
.notification-item {
    display: flex;
    padding: var(--space-lg) var(--space-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: var(--transition-normal);
    gap: var(--space-lg);
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: rgba(26, 32, 44, 0.4);
    backdrop-filter: blur(5px);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-xs);
    outline: none;
    border: 1px solid transparent;
}

.notification-item:focus {
    outline: none;
    border-color: rgba(247, 147, 26, 0.5);
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.2);
}

.notification-item:focus-visible {
    outline: 2px solid var(--bitcoin);
    outline-offset: 2px;
}

.notification-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    transition: var(--transition-normal);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.notification-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.notification-item:hover {
    background: rgba(45, 55, 72, 0.8);
    transform: translateX(8px) translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(247, 147, 26, 0.2);
}

.notification-item:hover::before {
    background: var(--gradient-bitcoin);
    width: 6px;
}

.notification-item:hover::after {
    opacity: 1;
}

.notification-item.unread {
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.2);
    box-shadow: 0 0 20px rgba(247, 147, 26, 0.1);
}

.notification-item.unread::before {
    background: var(--gradient-bitcoin);
    width: 6px;
}

.notification-item.unread .notification-indicator {
    position: absolute;
    top: var(--space-md);
    right: var(--space-lg);
    width: 10px;
    height: 10px;
    background: var(--bitcoin);
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(247, 147, 26, 0.6);
    animation: notificationPulse 2s infinite;
}

/* Enhanced Notification Animations */
@keyframes notificationPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
}

@keyframes notificationSlideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes notificationFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.notification-item.loading {
    pointer-events: none;
    opacity: 0.6;
}

.notification-item.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Performance optimizations */
.notification-item {
    will-change: transform, box-shadow;
}

.notification-icon {
    will-change: transform;
}

.notification-item:hover {
    will-change: auto;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .notification-item,
    .notification-icon,
    .notification-controls > *,
    .stat-card {
        transition: none;
        animation: none;
    }

    .notification-item:hover {
        transform: none;
    }

    .notification-icon:hover {
        transform: none;
    }
}

.notification-item.unread:hover {
    background: rgba(247, 147, 26, 0.15);
    transform: translateX(12px) translateY(-3px);
    box-shadow: 0 8px 30px rgba(247, 147, 26, 0.3);
}

/* Enhanced Notification Icon */
.notification-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    background: rgba(45, 55, 72, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--text-secondary);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    transition: var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.notification-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-slow);
}

.notification-item:hover .notification-icon {
    transform: scale(1.05) rotate(2deg);
    box-shadow: var(--shadow-lg);
}

.notification-item:hover .notification-icon::before {
    opacity: 1;
}

.notification-item:hover .notification-icon::after {
    opacity: 1;
}

/* Cryptocurrency-specific icon styles */
.notification-icon.bitcoin {
    color: var(--bitcoin);
    background: rgba(247, 147, 26, 0.15);
    border-color: rgba(247, 147, 26, 0.3);
    box-shadow: var(--shadow-bitcoin);
}

.notification-icon.ethereum {
    color: #627eea;
    background: rgba(98, 126, 234, 0.15);
    border-color: rgba(98, 126, 234, 0.3);
    box-shadow: 0 4px 15px rgba(98, 126, 234, 0.3);
}

.notification-icon.dogecoin {
    color: #c3a634;
    background: rgba(195, 166, 52, 0.15);
    border-color: rgba(195, 166, 52, 0.3);
    box-shadow: 0 4px 15px rgba(195, 166, 52, 0.3);
}

.notification-icon.alert {
    color: var(--danger);
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.notification-icon.success {
    color: var(--success);
    background: rgba(16, 185, 129, 0.15);
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.notification-icon.info {
    color: var(--info);
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Enhanced Notification Content */
.notification-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0; /* Prevent flex item overflow */
    position: relative;
}

.notification-title {
    font-weight: 700;
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
    line-height: 1.4;
    letter-spacing: 0.3px;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.notification-message {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-xs);
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
    opacity: 0.9;
}

.notification-time {
    font-size: 0.8rem;
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-weight: 500;
    opacity: 0.8;
}

.notification-time::before {
    content: '\f017';
    font-family: 'Font Awesome 5 Free';
    font-weight: 400;
    font-size: 0.75rem;
    opacity: 0.7;
    color: var(--bitcoin);
}

.notification-time:hover {
    opacity: 1;
    color: var(--text-secondary);
}

.notification-time:hover::before {
    opacity: 1;
    transform: scale(1.1);
}

/* Enhanced Notification Item Features */
.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.notification-category-badge {
    background: rgba(247, 147, 26, 0.2);
    color: #f7931a;
    font-size: 0.65rem;
    font-weight: 700;
    padding: 2px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notification-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 6px;
}

.notification-priority-text {
    font-size: 0.7rem;
    color: #ef4444;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.notification-priority-indicator {
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    border-radius: 0 2px 2px 0;
}

.notification-priority-indicator.high {
    background: linear-gradient(to bottom, #ef4444, #dc2626);
}

.notification-priority-indicator.medium {
    background: linear-gradient(to bottom, #f7931a, #ea580c);
}

/* Enhanced Notification Controls */
.notification-controls {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    margin-left: var(--space-sm);
    opacity: 0;
    transition: var(--transition-normal);
    position: relative;
}

.notification-item:hover .notification-controls {
    opacity: 1;
}

.notification-mark-read,
.notification-delete {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-tertiary);
    font-size: 0.8rem;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.notification-mark-read::before,
.notification-delete::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.notification-mark-read:hover::before,
.notification-delete:hover::before {
    opacity: 1;
}

.notification-mark-read:hover {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success);
    border-color: rgba(16, 185, 129, 0.4);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.notification-delete:hover {
    background: rgba(239, 68, 68, 0.2);
    color: var(--danger);
    border-color: rgba(239, 68, 68, 0.4);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.notification-mark-read:focus,
.notification-delete:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.notification-mark-read:active,
.notification-delete:active {
    transform: scale(0.95);
}

.notification-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.notification-action-btn {
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.3);
    color: #f7931a;
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.notification-action-btn:hover {
    background: rgba(247, 147, 26, 0.2);
    border-color: rgba(247, 147, 26, 0.5);
    transform: translateY(-1px);
}

/* Category-specific styles */
.notification-alert {
    border-left: 3px solid #ef4444;
}

.notification-sms {
    border-left: 3px solid #10b981;
}

.notification-system {
    border-left: 3px solid #6366f1;
}

.notification-price {
    border-left: 3px solid #f7931a;
}

.notification-general {
    border-left: 3px solid #a0aec0;
}

/* Priority-specific styles */
.notification-high-priority {
    background: rgba(239, 68, 68, 0.05);
}

.notification-medium-priority {
    background: rgba(247, 147, 26, 0.05);
}

.notification-low-priority {
    background: rgba(16, 185, 129, 0.05);
}

/* ===== RESPONSIVE NOTIFICATION STYLES ===== */
@media (max-width: 768px) {
    .notifications-page {
        padding: var(--space-lg);
        border-radius: var(--radius-lg);
    }

    .notifications-header {
        padding: var(--space-lg);
        margin-bottom: var(--space-lg);
    }

    .notifications-title {
        font-size: 2rem;
        gap: var(--space-md);
    }

    .notifications-subtitle {
        font-size: 1rem;
    }

    .notifications-stats {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
        margin-bottom: var(--space-lg);
    }

    .stat-card {
        padding: var(--space-lg);
        gap: var(--space-lg);
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .notification-item {
        padding: var(--space-md);
        gap: var(--space-md);
        margin-bottom: var(--space-sm);
    }

    .notification-item:hover {
        transform: translateX(4px) translateY(-1px);
    }

    .notification-item.unread:hover {
        transform: translateX(6px) translateY(-2px);
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .notification-title {
        font-size: 0.95rem;
    }

    .notification-message {
        font-size: 0.85rem;
    }

    .notification-time {
        font-size: 0.75rem;
    }

    .notification-controls {
        position: static;
        opacity: 1;
        flex-direction: row;
        margin-left: 0;
        margin-top: var(--space-sm);
        gap: var(--space-sm);
    }

    .notification-mark-read,
    .notification-delete {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .notifications-page {
        padding: var(--space-md);
        margin: var(--space-sm);
    }

    .notifications-header {
        padding: var(--space-md);
    }

    .notifications-title {
        font-size: 1.8rem;
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }

    .notifications-header-content {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }

    .stat-card {
        padding: var(--space-md);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .notification-item {
        padding: var(--space-sm) var(--space-md);
        flex-direction: column;
        gap: var(--space-sm);
    }

    .notification-content {
        order: 2;
    }

    .notification-icon {
        order: 1;
        align-self: flex-start;
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .notification-controls {
        order: 3;
        justify-content: flex-start;
    }
}

/* ===== ENHANCED NOTIFICATIONS PAGE STYLES ===== */
.notifications-page {
    padding: var(--space-xl);
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.95) 100%);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.notifications-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(247, 147, 26, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.notifications-page > * {
    position: relative;
    z-index: 1;
}

/* Enhanced Notifications Header */
.notifications-header {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.9), rgba(45, 55, 72, 0.9));
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    margin-bottom: var(--space-2xl);
    border: 1px solid rgba(247, 147, 26, 0.2);
    box-shadow: var(--shadow-2xl);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
}

.notifications-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-bitcoin);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.notifications-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(247, 147, 26, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
    pointer-events: none;
}

.notifications-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-xl);
    position: relative;
    z-index: 2;
}

.notifications-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    background: linear-gradient(135deg, #ffffff, #f7931a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.notifications-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: var(--space-sm) 0 0 0;
    opacity: 0.9;
}

.notifications-header-actions {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

/* Enhanced Statistics Cards */
.notifications-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.stat-card {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: var(--space-xl);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-bitcoin);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(247, 147, 26, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(247, 147, 26, 0.3);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: rgba(247, 147, 26, 0.15);
    color: var(--bitcoin);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-bitcoin);
    position: relative;
    z-index: 2;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(247, 147, 26, 0.4);
}

.stat-icon.unread {
    background: rgba(59, 130, 246, 0.15);
    color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.stat-icon.high-priority {
    background: rgba(239, 68, 68, 0.15);
    color: #ef4444;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.stat-icon.today {
    background: rgba(16, 185, 129, 0.15);
    color: #10b981;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.stat-content {
    flex: 1;
    position: relative;
    z-index: 2;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: var(--space-sm);
    background: linear-gradient(135deg, #ffffff, #f7931a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

/* Controls Section */
.notifications-controls {
    background: rgba(45, 55, 72, 0.5);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(74, 85, 104, 0.3);
    backdrop-filter: blur(10px);
}

.notifications-search {
    margin-bottom: 20px;
}

.search-input-container {
    position: relative;
    max-width: 400px;
}

.search-input-container i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 1rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

.search-clear {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
    z-index: 2;
}

.search-clear:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.notifications-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    color: #a0aec0;
    font-size: 0.9rem;
    font-weight: 600;
}

.filter-select {
    padding: 10px 15px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 8px;
    color: white;
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

/* Notifications Content */
.notifications-content {
    background: rgba(45, 55, 72, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(74, 85, 104, 0.3);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.notifications-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
    background: rgba(26, 32, 44, 0.5);
}

.list-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.list-view-options {
    display: flex;
    gap: 8px;
}

.view-option {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: #a0aec0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-option:hover,
.view-option.active {
    background: rgba(247, 147, 26, 0.2);
    color: #f7931a;
    border-color: rgba(247, 147, 26, 0.3);
}

/* Loading and Empty States */
.notifications-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #a0aec0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(247, 147, 26, 0.3);
    border-top: 3px solid #f7931a;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.notifications-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-icon {
    font-size: 4rem;
    color: #4a5568;
    margin-bottom: 20px;
}

.notifications-empty h3 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.notifications-empty p {
    color: #a0aec0;
    margin-bottom: 30px;
    max-width: 400px;
}

/* Enhanced Notification Items for Full Page */
.notifications-list {
    max-height: none;
    overflow: visible;
}

.notification-item-full {
    display: flex;
    align-items: flex-start;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.2);
    transition: all 0.3s ease;
    position: relative;
    background: rgba(26, 32, 44, 0.3);
}

.notification-item-full:hover {
    background: rgba(255, 255, 255, 0.02);
    transform: translateX(2px);
}

.notification-item-full.selected {
    background: rgba(247, 147, 26, 0.1);
    border-left: 4px solid #f7931a;
}

.notification-item-full.unread {
    background: rgba(59, 130, 246, 0.05);
    border-left: 3px solid #3b82f6;
}

.notification-checkbox {
    margin-right: 15px;
    margin-top: 5px;
    width: 18px;
    height: 18px;
    accent-color: #f7931a;
}

.notification-item-full .notification-icon {
    width: 50px;
    height: 50px;
    margin-right: 20px;
    margin-top: 0;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.notification-item-full .notification-content {
    flex: 1;
    min-width: 0;
}

.notification-item-full .notification-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: white;
}

.notification-item-full .notification-message {
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 12px;
    color: #a0aec0;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.notification-time-full {
    color: #718096;
    font-size: 0.85rem;
}

.notification-priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notification-priority-badge.priority-high {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.notification-priority-badge.priority-medium {
    background: rgba(247, 147, 26, 0.2);
    color: #f7931a;
}

.notification-priority-badge.priority-low {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.notification-actions-full {
    display: flex;
    gap: 8px;
    margin-left: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item-full:hover .notification-actions-full {
    opacity: 1;
}

.notification-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #a0aec0;
    font-size: 0.8rem;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.3s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-action-btn:hover {
    background: rgba(247, 147, 26, 0.2);
    color: #f7931a;
    border-color: rgba(247, 147, 26, 0.4);
    transform: scale(1.1);
}

/* Pagination */
.notifications-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: rgba(26, 32, 44, 0.5);
    border-top: 1px solid rgba(74, 85, 104, 0.3);
}

.pagination-btn {
    padding: 10px 20px;
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.3);
    border-radius: 6px;
    color: #f7931a;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.pagination-btn:hover:not(:disabled) {
    background: rgba(247, 147, 26, 0.2);
    border-color: rgba(247, 147, 26, 0.5);
    transform: translateY(-1px);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: #a0aec0;
    font-size: 0.9rem;
    font-weight: 600;
}
/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal:not([style*="display: none"]) {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: linear-gradient(135deg, #1a202c, #2d3748);
    border-radius: 12px;
    border: 1px solid rgba(74, 85, 104, 0.5);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal:not([style*="display: none"]) .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
}

.modal-header h2 {
    color: white;
    font-size: 1.5rem;
    margin: 0;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    color: #a0aec0;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 30px;
    border-top: 1px solid rgba(74, 85, 104, 0.3);
    background: rgba(26, 32, 44, 0.3);
}

/* Settings Styles */
.settings-section {
    margin-bottom: 30px;
}

.settings-section h3 {
    color: #f7931a;
    font-size: 1.2rem;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
}

.setting-item {
    margin-bottom: 15px;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    cursor: pointer;
    font-size: 0.95rem;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: #f7931a;
    border-color: #f7931a;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.setting-select,
.setting-input {
    width: 100%;
    padding: 10px 15px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 6px;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.setting-select:focus,
.setting-input:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #a0aec0;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px 15px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 6px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-page {
        padding: 15px;
    }

    .notifications-header {
        padding: 20px;
    }

    .notifications-header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .notifications-title {
        font-size: 2rem;
    }

    .notifications-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .stat-card {
        padding: 20px;
    }

    .notifications-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .notifications-list-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .notification-item-full {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
    }

    .notification-checkbox {
        margin: 0;
        align-self: flex-start;
    }

    .notification-item-full .notification-icon {
        margin: 0;
        align-self: flex-start;
    }

    .notification-actions-full {
        opacity: 1;
        margin: 0;
        align-self: flex-end;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-header,
    .modal-footer {
        padding: 15px 20px;
    }
}

@media (max-width: 480px) {
    .notifications-title {
        font-size: 1.8rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .notifications-stats {
        grid-template-columns: 1fr;
    }

    .search-input-container {
        max-width: 100%;
    }

    .notifications-filters {
        gap: 15px;
    }

    .filter-select {
        min-width: 100%;
    }
}







/* Statistics Cards */
.notifications-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(74, 85, 104, 0.3);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: rgba(247, 147, 26, 0.2);
    color: #f7931a;
    transition: all 0.3s ease;
}

.stat-icon.unread {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.stat-icon.high-priority {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.stat-icon.today {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.stat-label {
    color: #a0aec0;
    font-size: 0.9rem;
    margin-top: 4px;
}

/* Controls Section */
.notifications-controls {
    background: rgba(45, 55, 72, 0.5);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(74, 85, 104, 0.3);
    backdrop-filter: blur(10px);
}

.notifications-search {
    margin-bottom: 20px;
}

.search-input-container {
    position: relative;
    max-width: 400px;
}

.search-input-container i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 1rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

.search-clear {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
    z-index: 2;
}

.search-clear:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.notifications-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    color: #a0aec0;
    font-size: 0.9rem;
    font-weight: 600;
}

.filter-select {
    padding: 10px 15px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 8px;
    color: white;
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

/* Notifications Content */
.notifications-content {
    background: rgba(45, 55, 72, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(74, 85, 104, 0.3);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.notifications-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
    background: rgba(26, 32, 44, 0.5);
}

.list-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.list-view-options {
    display: flex;
    gap: 8px;
}

.view-option {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: #a0aec0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-option:hover,
.view-option.active {
    background: rgba(247, 147, 26, 0.2);
    color: #f7931a;
    border-color: rgba(247, 147, 26, 0.3);
}

/* Loading and Empty States */
.notifications-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #a0aec0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(247, 147, 26, 0.3);
    border-top: 3px solid #f7931a;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.notifications-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-icon {
    font-size: 4rem;
    color: #4a5568;
    margin-bottom: 20px;
}

.notifications-empty h3 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.notifications-empty p {
    color: #a0aec0;
    margin-bottom: 30px;
    max-width: 400px;
}

/* Enhanced Notifications Page Styles */
.notifications-page {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.notifications-header {
    background: linear-gradient(135deg, #1a202c, #2d3748);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(74, 85, 104, 0.3);
}

.notifications-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.notifications-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #f7931a;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.notifications-subtitle {
    color: #a0aec0;
    font-size: 1.1rem;
    margin: 8px 0 0 0;
}

.notifications-header-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}



/* Loading and Empty States */
.notifications-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #a0aec0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(247, 147, 26, 0.3);
    border-top: 3px solid #f7931a;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.notifications-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-icon {
    font-size: 4rem;
    color: #4a5568;
    margin-bottom: 20px;
}

.notifications-empty h3 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.notifications-empty p {
    color: #a0aec0;
    margin-bottom: 30px;
    max-width: 400px;
}

/* Enhanced Notification Items for Full Page */
.notifications-list {
    max-height: none;
    overflow: visible;
}

.notification-item-full {
    display: flex;
    align-items: flex-start;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.notification-item-full:hover {
    background: rgba(255, 255, 255, 0.02);
}

.notification-item-full.selected {
    background: rgba(247, 147, 26, 0.1);
    border-left: 4px solid #f7931a;
}

.notification-checkbox {
    margin-right: 15px;
    margin-top: 5px;
}

.notification-item-full .notification-icon {
    width: 50px;
    height: 50px;
    margin-right: 20px;
    margin-top: 0;
}

.notification-item-full .notification-content {
    flex: 1;
    min-width: 0;
}

.notification-item-full .notification-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.notification-item-full .notification-message {
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 12px;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.notification-time-full {
    color: #718096;
    font-size: 0.85rem;
}

.notification-actions-full {
    display: flex;
    gap: 8px;
    margin-left: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item-full:hover .notification-actions-full {
    opacity: 1;
}

/* Pagination */
.notifications-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: rgba(26, 32, 44, 0.5);
    border-top: 1px solid rgba(74, 85, 104, 0.3);
}

/* Enhanced Pagination Buttons */
.pagination-btn {
    padding: var(--space-md) var(--space-lg);
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.3);
    border-radius: var(--radius-md);
    color: var(--bitcoin);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    position: relative;
    overflow: hidden;
    min-height: 44px;
    text-decoration: none;
}

.pagination-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.pagination-btn:hover:not(:disabled) {
    background: rgba(247, 147, 26, 0.2);
    border-color: rgba(247, 147, 26, 0.5);
    transform: translateY(-2px);
    box-shadow: var(--shadow-bitcoin);
}

.pagination-btn:hover:not(:disabled)::before {
    opacity: 1;
}

.pagination-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.pagination-btn:active:not(:disabled) {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.pagination-btn i {
    transition: var(--transition-normal);
}

.pagination-btn:hover:not(:disabled) i {
    transform: scale(1.1);
}

.pagination-info {
    color: #a0aec0;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #1a202c, #2d3748);
    border-radius: 12px;
    border: 1px solid rgba(74, 85, 104, 0.5);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
}

.modal-header h2 {
    color: white;
    font-size: 1.5rem;
    margin: 0;
}

/* Enhanced Modal Close Button */
.modal-close {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-tertiary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.modal-close::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.modal-close:hover {
    color: var(--danger);
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.modal-close:hover::before {
    opacity: 1;
}

.modal-close:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.modal-close:active {
    transform: scale(0.95);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 30px;
    border-top: 1px solid rgba(74, 85, 104, 0.3);
    background: rgba(26, 32, 44, 0.3);
}

/* Settings Styles */
.settings-section {
    margin-bottom: 30px;
}

.settings-section h3 {
    color: #f7931a;
    font-size: 1.2rem;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
}

.setting-item {
    margin-bottom: 15px;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    cursor: pointer;
    font-size: 0.95rem;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: #f7931a;
    border-color: #f7931a;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.setting-select,
.setting-input {
    width: 100%;
    padding: 10px 15px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 6px;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.setting-select:focus,
.setting-input:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #a0aec0;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px 15px;
    background: rgba(26, 32, 44, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.5);
    border-radius: 6px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-page {
        padding: 15px;
    }

    .notifications-header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .notifications-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .notifications-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .notifications-list-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-body {
        padding: 20px;
    }
}

/* Empty notification state */
.notification-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #718096;
    background: rgba(45, 55, 72, 0.3);
    border-radius: 8px;
    margin: 20px;
}

.notification-empty-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
    opacity: 0.6;
    color: #a0aec0;
    background: rgba(255, 255, 255, 0.05);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.notification-empty-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% {
        transform: rotate(0deg);
        opacity: 0;
    }
    25% {
        opacity: 0.5;
    }
    50% {
        transform: rotate(180deg);
        opacity: 0;
    }
    100% {
        transform: rotate(360deg);
        opacity: 0;
    }
}

.notification-empty-text {
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.5;
    max-width: 200px;
    color: #a0aec0;
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    bottom: 24px;
    right: 24px;
    background: linear-gradient(135deg, #1a202c, #2d3748);
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(74, 85, 104, 0.8);
    display: flex;
    flex-direction: column;
    z-index: 9999;
    max-width: 420px;
    min-width: 320px;
    transform: translateY(100px) scale(0.95);
    opacity: 0;
    transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.5s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
    overflow: hidden;
    visibility: hidden; /* Start hidden to prevent flash */
}

/* Toast Priority Variants */
.toast-notification.toast-high {
    border-left: 4px solid #ef4444;
    box-shadow: 0 15px 35px rgba(239, 68, 68, 0.3), 0 5px 15px rgba(0, 0, 0, 0.2);
}

.toast-notification.toast-medium {
    border-left: 4px solid #f7931a;
}

.toast-notification.toast-low {
    border-left: 4px solid #10b981;
}

/* Toast Progress Bar */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.toast-progress-bar {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f7931a, #ff7e5f);
    transform: translateX(-100%);
    animation: progressBar 5s linear forwards;
}

@keyframes progressBar {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Toast Main Content */
.toast-main-content {
    display: flex;
    align-items: center;
    padding: 18px 20px;
}

/* Toast Actions */
.toast-actions {
    display: flex;
    gap: 8px;
    margin-left: 12px;
}

/* Enhanced Toast Action Buttons */
.toast-action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-tertiary);
    font-size: 0.85rem;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.toast-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.toast-action-btn:hover {
    background: rgba(247, 147, 26, 0.2);
    color: var(--bitcoin);
    border-color: rgba(247, 147, 26, 0.4);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3);
}

.toast-action-btn:hover::before {
    opacity: 1;
}

.toast-action-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.toast-action-btn:active {
    transform: scale(0.95);
}

/* Toast Timestamp */
.toast-timestamp {
    font-size: 0.7rem;
    color: #718096;
    margin-top: 4px;
    opacity: 0.8;
}

.toast-notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #f7931a, #ff7e5f);
}

.toast-notification::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(247, 147, 26, 0.1), transparent 70%);
    pointer-events: none;
}

.toast-notification.show {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

.toast-notification:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 10px 20px rgba(0, 0, 0, 0.3);
}

.toast-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(45, 55, 72, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: #a0aec0;
    margin-right: 18px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.toast-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.toast-notification:hover .toast-icon::after {
    opacity: 1;
}

.toast-icon.bitcoin {
    color: #f7931a;
    background: rgba(247, 147, 26, 0.15);
}

.toast-icon.ethereum {
    color: #627eea;
    background: rgba(98, 126, 234, 0.15);
}

.toast-icon.dogecoin {
    color: #c3a634;
    background: rgba(195, 166, 52, 0.15);
}

.toast-icon.sms,
.notification-icon.sms {
    color: #10b981;
    background: rgba(16, 185, 129, 0.15);
}

/* Navigation Icons */
.toast-icon.home,
.notification-icon.home {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.15);
}

.toast-icon.history,
.notification-icon.history {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.15);
}

.toast-icon.chart-line,
.notification-icon.chart-line {
    color: #10b981;
    background: rgba(16, 185, 129, 0.15);
}

.toast-icon.balance-scale,
.notification-icon.balance-scale {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.15);
}

.toast-icon.newspaper,
.notification-icon.newspaper {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.15);
}

.toast-icon.exchange-alt,
.notification-icon.exchange-alt {
    color: #ec4899;
    background: rgba(236, 72, 153, 0.15);
}

.toast-icon.calculator,
.notification-icon.calculator {
    color: #14b8a6;
    background: rgba(20, 184, 166, 0.15);
}

.toast-icon.bell,
.notification-icon.bell {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.15);
}

.toast-content {
    flex: 1;
    padding-right: 10px;
}

.toast-title {
    font-weight: 700;
    font-size: 1.05rem;
    color: #ffffff;
    margin-bottom: 6px;
    letter-spacing: 0.3px;
    line-height: 1.3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.toast-message {
    font-size: 0.9rem;
    color: #a0aec0;
    line-height: 1.5;
}

.toast-close {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #a0aec0;
    font-size: 0.9rem;
    cursor: pointer;
    padding: 8px;
    margin-left: 10px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    width: 28px;
    height: 28px;
    flex-shrink: 0;
}

.toast-close:hover {
    background: rgba(247, 147, 26, 0.1);
    color: #f7931a;
    border-color: rgba(247, 147, 26, 0.3);
    transform: rotate(90deg);
}

/* Light Theme Notifications */
body.light-theme .notification-bell i {
    color: #64748b;
}

body.light-theme .notification-bell:hover i {
    color: #f7931a;
}

body.light-theme .notification-bell.active i {
    color: #f7931a;
}

body.light-theme .notification-dropdown {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);
}

body.light-theme .notification-dropdown::before {
    background: #ffffff;
    border-left: 1px solid #e2e8f0;
    border-top: 1px solid #e2e8f0;
}

body.light-theme .notification-header {
    background: linear-gradient(to right, #f8fafc, #f1f5f9);
    border-bottom: 1px solid #e2e8f0;
}

body.light-theme .notification-header h3 {
    color: #1e293b;
    text-shadow: none;
}

body.light-theme .notification-clear {
    background: rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.05);
    color: #64748b;
}

body.light-theme .notification-clear:hover {
    background: rgba(247, 147, 26, 0.05);
    color: #f7931a;
    border-color: rgba(247, 147, 26, 0.2);
}

body.light-theme .notification-list::-webkit-scrollbar-track {
    background: #f1f5f9;
}

body.light-theme .notification-list::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
}

body.light-theme .notification-item {
    border-bottom: 1px solid #e2e8f0;
}

body.light-theme .notification-item:hover {
    background: #f8fafc;
}

body.light-theme .notification-item.unread {
    background: rgba(247, 147, 26, 0.05);
}

body.light-theme .notification-item.unread:hover {
    background: rgba(247, 147, 26, 0.08);
}

body.light-theme .notification-icon {
    background: #f1f5f9;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    color: #64748b;
}

body.light-theme .notification-title {
    color: #1e293b;
}

body.light-theme .notification-message {
    color: #64748b;
}

body.light-theme .notification-time {
    color: #94a3b8;
}

body.light-theme .notification-empty {
    background: rgba(241, 245, 249, 0.5);
}

body.light-theme .notification-empty-icon {
    background: #f8fafc;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.05);
}

/* Light Theme Toast */
body.light-theme .toast-notification {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid #e2e8f0;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.05);
}

body.light-theme .toast-notification::before {
    background: linear-gradient(to bottom, #f7931a, #ff7e5f);
}

body.light-theme .toast-notification::after {
    background: radial-gradient(circle at top right, rgba(247, 147, 26, 0.05), transparent 70%);
}

body.light-theme .toast-notification:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 10px 20px rgba(0, 0, 0, 0.05);
}

body.light-theme .toast-icon {
    background: #f1f5f9;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    color: #64748b;
}

body.light-theme .toast-title {
    color: #1e293b;
    text-shadow: none;
}

body.light-theme .toast-message {
    color: #64748b;
}

body.light-theme .toast-close {
    background: rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.05);
    color: #94a3b8;
}

body.light-theme .toast-close:hover {
    background: rgba(247, 147, 26, 0.05);
    color: #f7931a;
    border-color: rgba(247, 147, 26, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
    z-index: 1001;
    position: relative;
}

.logo::before {
    content: '';
    position: absolute;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 149, 0, 0.15) 0%, transparent 70%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.logo:hover::before {
    opacity: 1;
}

.logo-icon {
    font-size: 1.8rem;
    color: var(--bitcoin);
    background: rgba(255, 149, 0, 0.1);
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-round);
    box-shadow: var(--shadow-bitcoin);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.logo-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.logo:hover .logo-icon {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 149, 0, 0.4);
}

.logo:hover .logo-icon::after {
    opacity: 1;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--gradient-bitcoin);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.5px;
    position: relative;
}

.logo-text span {
    font-weight: 600;
    opacity: 0.9;
}

.nav {
    display: flex;
    gap: var(--space-md);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: var(--text-sm);
    position: relative;
    padding: var(--space-sm) var(--space-md);
    text-decoration: none;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: all 0.3s ease;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--bitcoin);
    transform: translateX(-50%);
    transition: width 0.3s ease;
    border-radius: var(--radius-full);
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link:hover::before {
    width: 60%;
}

.nav-link.active {
    color: var(--bitcoin);
    background: rgba(255, 149, 0, 0.08);
}

.nav-link.active::before {
    width: 80%;
}

.nav-link i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.nav-link:hover i {
    transform: translateY(-2px);
}

/* ===== MOBILE BOTTOM NAVIGATION ===== */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.95), rgba(45, 55, 72, 0.95));
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(247, 147, 26, 0.2);
    z-index: 1000;
    display: none;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.mobile-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    max-width: 100%;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    text-decoration: none;
    color: var(--text-tertiary);
    min-width: 60px;
    position: relative;
    overflow: hidden;
    background: none;
    border: none;
    cursor: pointer;
}

.mobile-nav-item i {
    font-size: 1.2rem;
    margin-bottom: 2px;
    transition: var(--transition-normal);
}

.nav-label {
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
    transition: var(--transition-normal);
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
    color: var(--bitcoin);
    transform: translateY(-2px);
}

.mobile-nav-item:hover i,
.mobile-nav-item.active i {
    transform: scale(1.1);
    color: var(--bitcoin);
}

.mobile-nav-item:hover .nav-label,
.mobile-nav-item.active .nav-label {
    opacity: 1;
    color: var(--bitcoin);
}

.mobile-nav-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--gradient-bitcoin);
    border-radius: var(--radius-full);
}

/* Mobile Navigation Badge */
.mobile-nav-badge {
    position: absolute;
    top: 2px;
    right: 8px;
    background: var(--danger);
    color: white;
    font-size: 0.6rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Mobile More Menu */
.mobile-nav-more {
    position: relative;
}

.mobile-more-btn {
    background: none;
    border: none;
}

.mobile-more-menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.95), rgba(45, 55, 72, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(247, 147, 26, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-sm);
    margin-bottom: var(--space-sm);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-2xl);
}

.mobile-more-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-more-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-secondary);
    transition: var(--transition-normal);
    margin-bottom: var(--space-xs);
}

.mobile-more-item:last-child {
    margin-bottom: 0;
}

.mobile-more-item:hover,
.mobile-more-item.active {
    background: rgba(247, 147, 26, 0.1);
    color: var(--bitcoin);
    transform: translateX(4px);
}

.mobile-more-item i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(247, 147, 26, 0.3);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Mobile Header Controls */
.mobile-header-controls {
    display: none;
}

.mobile-notification-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.2);
    color: var(--bitcoin);
    text-decoration: none;
    transition: var(--transition-normal);
    position: relative;
}

.mobile-notification-btn:hover {
    background: rgba(247, 147, 26, 0.2);
    transform: scale(1.05);
}

.mobile-notification-btn .notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--danger);
    color: white;
    font-size: 0.6rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== RESPONSIVE NAVIGATION ===== */
@media (max-width: 992px) {
    /* Hide desktop navigation */
    .desktop-nav {
        display: none;
    }

    /* Show mobile header controls */
    .mobile-header-controls {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
    }

    /* Show mobile bottom navigation */
    .mobile-bottom-nav {
        display: block;
    }

    /* Add bottom padding to main content for mobile nav */
    .main-content {
        padding-bottom: 80px;
    }

    /* Adjust header for mobile */
    .header {
        position: sticky;
        top: 0;
        z-index: 999;
    }

    .header-container {
        justify-content: space-between;
    }
}

/* Desktop Navigation Styles */
@media (min-width: 993px) {
    /* Hide mobile elements on desktop */
    .mobile-bottom-nav,
    .mobile-header-controls {
        display: none;
    }

    /* Show desktop navigation */
    .desktop-nav {
        display: block;
    }

    .desktop-nav .nav {
        display: flex;
        align-items: center;
        gap: var(--space-md);
    }

    .desktop-nav .nav-item {
        position: relative;
    }

    .desktop-nav .nav-link {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
        padding: var(--space-sm) var(--space-md);
        border-radius: var(--radius-md);
        text-decoration: none;
        color: var(--text-secondary);
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .desktop-nav .nav-link::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background: var(--gradient-bitcoin);
        transition: var(--transition-normal);
    }

    .desktop-nav .nav-link:hover::before,
    .desktop-nav .nav-link.active::before {
        width: 100%;
    }

    .desktop-nav .nav-link:hover,
    .desktop-nav .nav-link.active {
        color: var(--bitcoin);
        background: rgba(247, 147, 26, 0.1);
    }

    .desktop-nav .nav-link i {
        transition: var(--transition-normal);
    }

    .desktop-nav .nav-link:hover i {
        transform: scale(1.1);
        color: var(--bitcoin);
    }
}

/* ===== BITCOIN CALENDAR STYLES ===== */
.calendar-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(247, 147, 26, 0.1);
}

.calendar-nav-controls {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.calendar-nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: rgba(247, 147, 26, 0.1);
    color: var(--bitcoin);
    cursor: pointer;
    transition: var(--transition-normal);
}

.calendar-nav-btn:hover {
    background: rgba(247, 147, 26, 0.2);
    transform: scale(1.05);
}

.calendar-current-month {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
}

.month-name {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--bitcoin);
}

.year {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
}

.calendar-view-toggle {
    display: flex;
    gap: var(--space-xs);
}

.view-toggle-btn {
    padding: var(--space-sm) var(--space-md);
    border: 1px solid rgba(247, 147, 26, 0.2);
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-normal);
}

.view-toggle-btn.active,
.view-toggle-btn:hover {
    background: rgba(247, 147, 26, 0.1);
    color: var(--bitcoin);
    border-color: rgba(247, 147, 26, 0.3);
}

.calendar-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.summary-card {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(247, 147, 26, 0.1);
    transition: var(--transition-normal);
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(247, 147, 26, 0.2);
}

.summary-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: var(--radius-md);
    background: rgba(247, 147, 26, 0.1);
    color: var(--bitcoin);
    font-size: var(--text-xl);
}

.summary-content h3 {
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.summary-value.positive {
    color: var(--success);
}

.summary-value.negative {
    color: var(--danger);
}

.summary-date {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
}

.calendar-container {
    margin-bottom: var(--space-lg);
}

.calendar-grid {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(247, 147, 26, 0.1);
    overflow: hidden;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: rgba(247, 147, 26, 0.1);
}

.calendar-day-header {
    padding: var(--space-md);
    text-align: center;
    font-weight: 600;
    color: var(--bitcoin);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: var(--text-sm);
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
    position: relative;
    min-height: 80px;
    padding: var(--space-sm);
    border: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.calendar-day:hover {
    background: rgba(247, 147, 26, 0.05);
}

.calendar-day.empty {
    cursor: default;
    opacity: 0.3;
}

.calendar-day.today {
    background: rgba(247, 147, 26, 0.1);
    border-color: var(--bitcoin);
}

.calendar-day.price-high {
    background: rgba(34, 197, 94, 0.1);
    border-color: var(--success);
}

.calendar-day.price-low {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger);
}

.calendar-day.significant-move {
    background: rgba(168, 85, 247, 0.1);
    border-color: #a855f7;
}

.calendar-day.alert-triggered {
    position: relative;
}

.calendar-day.alert-triggered::after {
    content: '';
    position: absolute;
    top: 4px;
    right: 4px;
    width: 8px;
    height: 8px;
    background: var(--danger);
    border-radius: 50%;
    animation: badgePulse 2s infinite;
}

.day-number {
    font-weight: 600;
    color: var(--text-primary);
}

.day-indicators {
    display: flex;
    gap: var(--space-xs);
    margin-top: var(--space-xs);
}

.day-indicators i {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
}

/* Calendar Modal */
.calendar-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--bg-dark);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(247, 147, 26, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    margin: 0;
    color: var(--bitcoin);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: var(--text-lg);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-lg);
}

.event-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.price-info,
.events-info,
.alerts-info {
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.price-info h4,
.events-info h4,
.alerts-info h4 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-secondary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-value {
    font-size: var(--text-2xl);
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.price-change {
    font-size: var(--text-lg);
    font-weight: 600;
    margin: var(--space-xs) 0 0 0;
}

.price-change.positive {
    color: var(--success);
}

.price-change.negative {
    color: var(--danger);
}

.events-info ul {
    margin: 0;
    padding-left: var(--space-md);
}

.events-info li {
    margin-bottom: var(--space-xs);
    color: var(--text-secondary);
}

.no-events {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-tertiary);
}

.no-events i {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-md);
    color: var(--text-tertiary);
}

.calendar-legend {
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(247, 147, 26, 0.1);
}

.calendar-legend h3 {
    margin: 0 0 var(--space-md) 0;
    color: var(--bitcoin);
    font-size: var(--text-lg);
}

.legend-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-sm);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: var(--radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-color.price-high {
    background: rgba(34, 197, 94, 0.3);
    border-color: var(--success);
}

.legend-color.price-low {
    background: rgba(239, 68, 68, 0.3);
    border-color: var(--danger);
}

.legend-color.alert-triggered {
    background: rgba(247, 147, 26, 0.3);
    border-color: var(--bitcoin);
}

.legend-color.significant-move {
    background: rgba(168, 85, 247, 0.3);
    border-color: #a855f7;
}

.legend-color.today {
    background: rgba(247, 147, 26, 0.5);
    border-color: var(--bitcoin);
}

.legend-item span {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* Mobile Calendar Responsive */
@media (max-width: 768px) {
    .calendar-nav {
        flex-direction: column;
        gap: var(--space-md);
    }

    .calendar-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }

    .calendar-day {
        min-height: 60px;
        padding: var(--space-xs);
    }

    .calendar-day-header {
        padding: var(--space-sm);
        font-size: var(--text-xs);
    }

    .day-number {
        font-size: var(--text-sm);
    }

    .legend-items {
        grid-template-columns: 1fr;
    }
}

/* Mobile Menu Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: 999;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

@media (min-width: 993px) {
    .mobile-overlay {
        display: none;
    }
}

@media (max-width: 576px) {
    .logo-text {
        font-size: 1.3rem;
    }

    .logo-icon {
        width: 38px;
        height: 38px;
        font-size: 1.5rem;
    }

    .header-container {
        height: 50px;
    }
}

/* ===== HERO SECTION ===== */
.hero {
    padding: var(--space-xxl) 0;
    text-align: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(0, 184, 148, 0.1) 100%);
    z-index: var(--z-negative);
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: var(--space-md);
    background: linear-gradient(to right, var(--primary-light), var(--info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto var(--space-xl);
}

/* ===== CARDS ===== */
.card {
    background: var(--bg-dark);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    box-shadow: var(--shadow-card);
    margin-bottom: var(--space-xl);
    border: 1px solid rgba(247, 147, 26, 0.15);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-bitcoin);
    z-index: 1;
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-glass);
    pointer-events: none;
    opacity: 0.7;
}

.card-content {
    position: relative;
    z-index: 2;
}

.card-glow {
    position: absolute;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, var(--bitcoin-glow) 0%, transparent 70%);
    border-radius: 50%;
    top: -75px;
    right: -75px;
    opacity: 0.3;
    z-index: 0;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.card-subtitle {
    font-size: 1rem;
    color: var(--text-tertiary);
    margin-bottom: var(--space-lg);
}

/* ===== ENHANCED FORMS ===== */
.form-group {
    margin-bottom: var(--space-lg);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--text-sm);
    transition: var(--transition-normal);
    position: relative;
}

.form-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-bitcoin);
    transition: var(--transition-normal);
    border-radius: var(--radius-full);
}

.form-group:focus-within .form-label::after {
    width: 30px;
}

.form-control {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: var(--bg-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: 'Poppins', sans-serif;
    transition: var(--transition-normal);
    position: relative;
    box-shadow: var(--shadow-sm);
}

.form-control:hover {
    border-color: rgba(247, 147, 26, 0.3);
    background: rgba(26, 32, 44, 0.9);
}

.form-control:focus {
    outline: none;
    border-color: var(--bitcoin);
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.2);
    background: var(--bg-medium);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--text-muted);
    transition: var(--transition-normal);
}

.form-control:focus::placeholder {
    color: rgba(160, 174, 192, 0.6);
    transform: translateX(5px);
}

.form-text {
    display: block;
    margin-top: var(--space-xs);
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    transition: var(--transition-normal);
}

.form-group:focus-within .form-text {
    color: var(--bitcoin-light);
}

/* Enhanced Select Wrapper */
.select-wrapper {
    position: relative;
}

.select-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.05) 0%, transparent 100%);
    border-radius: var(--radius-md);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.select-wrapper:hover::before {
    opacity: 1;
}

.select-wrapper select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: var(--space-2xl);
    cursor: pointer;
}

.select-wrapper i {
    position: absolute;
    right: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    pointer-events: none;
    transition: var(--transition-normal);
    font-size: 0.9rem;
}

.select-wrapper:hover i {
    color: var(--bitcoin);
    transform: translateY(-50%) scale(1.1);
}

.select-wrapper select:focus + i {
    color: var(--bitcoin);
    transform: translateY(-50%) rotate(180deg);
}

/* Enhanced Input Wrapper */
.input-wrapper {
    position: relative;
}

.input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.05) 0%, transparent 100%);
    border-radius: var(--radius-md);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.input-wrapper:hover::before {
    opacity: 1;
}

.input-prefix {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition-normal);
    z-index: 2;
}

.input-wrapper:focus-within .input-prefix {
    color: var(--bitcoin);
    transform: translateY(-50%) scale(1.05);
}

.input-wrapper input[type="number"] {
    padding-left: calc(var(--space-md) + 1.2rem);
}

.input-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    transition: var(--transition-normal);
    z-index: 2;
}

.input-wrapper:focus-within .input-icon {
    color: var(--bitcoin);
    transform: translateY(-50%) scale(1.1);
}

.input-wrapper input[type="tel"] {
    padding-left: calc(var(--space-md) + 1.8rem);
}

/* Form Validation States */
.form-control.is-valid {
    border-color: var(--success);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.2);
}

.form-control.is-invalid {
    border-color: var(--danger);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
}

.form-feedback {
    display: block;
    margin-top: var(--space-xs);
    font-size: var(--text-xs);
    font-weight: 500;
}

.form-feedback.valid-feedback {
    color: var(--success);
}

.form-feedback.invalid-feedback {
    color: var(--danger);
}

/* Form Row for Multiple Inputs */
.form-row {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.form-row .form-group {
    flex: 1;
    min-width: 200px;
}

/* Responsive Form Styles */
@media (max-width: 768px) {
    .form-control {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.9rem;
    }

    .form-label {
        font-size: 0.85rem;
    }

    .form-row {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .form-row .form-group {
        min-width: auto;
    }

    .input-wrapper input[type="number"] {
        padding-left: calc(var(--space-sm) + 1rem);
    }

    .input-wrapper input[type="tel"] {
        padding-left: calc(var(--space-sm) + 1.5rem);
    }

    .input-prefix {
        left: var(--space-sm);
        font-size: 0.85rem;
    }

    .input-icon {
        left: var(--space-sm);
        font-size: 0.9rem;
    }

    .select-wrapper i {
        right: var(--space-sm);
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .form-control {
        padding: var(--space-sm);
        font-size: 0.85rem;
    }

    .form-group {
        margin-bottom: var(--space-md);
    }

    .form-label {
        font-size: 0.8rem;
        margin-bottom: var(--space-xs);
    }

    .form-text {
        font-size: 0.7rem;
    }
}

.form-control {
    width: 100%;
    padding: var(--space-md);
    background: var(--bg-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: 'Poppins', sans-serif;
    transition: var(--transition-normal);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

.form-control::placeholder {
    color: var(--text-muted);
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%238a8a8a' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right var(--space-md) center;
    padding-right: var(--space-xl);
}

/* ===== ENHANCED BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: var(--transition-normal);
    cursor: pointer;
    border: none;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    gap: var(--space-sm);
    position: relative;
    overflow: hidden;
    z-index: 1;
    text-decoration: none;
    box-shadow: var(--shadow-button);
    min-height: 44px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    transform: translateY(100%);
    transition: var(--transition-normal);
    opacity: 0.8;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn:hover::before {
    transform: translateY(0);
}

.btn:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.btn-primary {
    background: var(--gradient-bitcoin);
    color: #ffffff;
    border: none;
    box-shadow: var(--shadow-bitcoin);
}

.btn-primary::before {
    background: linear-gradient(135deg, var(--bitcoin-light), var(--bitcoin));
}

.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(247, 147, 26, 0.4);
}

/* Enhanced Advanced Button */
.btn-advanced {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.9), rgba(74, 85, 104, 0.9));
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.9rem;
    font-family: 'Poppins', sans-serif;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    min-height: 44px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

.btn-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.btn-advanced:hover {
    background: linear-gradient(135deg, rgba(74, 85, 104, 0.9), rgba(113, 128, 150, 0.9));
    border-color: rgba(247, 147, 26, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-advanced:hover::before {
    opacity: 1;
}

.btn-advanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.3);
}

.btn-advanced:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.btn-advanced i {
    font-size: 1.1rem;
    color: var(--bitcoin);
    transition: var(--transition-normal);
}

.btn-advanced:hover i {
    color: var(--bitcoin-light);
    transform: scale(1.1) rotate(5deg);
}

.header-advanced-btn {
    margin-left: auto;
}


.btn-secondary {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-sm);
}

.btn-secondary::before {
    background: rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--text-primary);
    box-shadow: 0 4px 10px rgba(16, 185, 129, 0.3);
    border: 1px solid rgba(16, 185, 129, 0.5);
}

.btn-success:hover {
    box-shadow: 0 6px 15px rgba(16, 185, 129, 0.4);
}

.btn-danger {
    background: var(--gradient-danger);
    color: var(--text-primary);
    box-shadow: 0 4px 10px rgba(220, 38, 38, 0.3);
    border: 1px solid rgba(220, 38, 38, 0.5);
}

.btn-danger::before {
    background: linear-gradient(135deg, var(--danger-light), var(--danger));
}

.btn-danger:hover {
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.btn-info {
    background: var(--gradient-info);
    color: var(--text-primary);
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.5);
}

.btn-info:hover {
    box-shadow: 0 6px 15px rgba(59, 130, 246, 0.4);
}

.btn-outline {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
}

.btn-outline:hover {
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.btn-outline-primary {
    background: rgba(247, 147, 26, 0.05);
    border: 1px solid rgba(247, 147, 26, 0.3);
    color: var(--bitcoin);
}

.btn-outline-primary:hover {
    background: rgba(247, 147, 26, 0.1);
    border-color: var(--bitcoin);
}

.btn-outline-secondary {
    background: rgba(136, 84, 208, 0.05);
    border: 1px solid rgba(136, 84, 208, 0.3);
    color: var(--secondary);
}

.btn-outline-secondary:hover {
    background: rgba(136, 84, 208, 0.1);
    border-color: var(--secondary);
}

.btn-outline-success {
    background: rgba(16, 185, 129, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: var(--success);
}

.btn-outline-success:hover {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success);
}

.btn-outline-danger {
    background: rgba(220, 38, 38, 0.05);
    border: 1px solid rgba(220, 38, 38, 0.3);
    color: var(--danger);
}

.btn-outline-danger:hover {
    background: rgba(220, 38, 38, 0.1);
    border-color: var(--danger);
}

.btn-sm {
    padding: var(--space-sm) var(--space-md);
    font-size: 0.875rem;
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: 1.125rem;
    border-radius: var(--radius-lg);
}

.btn-block {
    width: 100%;
}

.btn-group {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

/* Responsive Button Styles */
@media (max-width: 768px) {
    .btn {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.9rem;
        min-height: 40px;
    }

    .btn-lg {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.85rem;
        min-height: 36px;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-height: 32px;
    }

    .btn i {
        font-size: 0.9rem;
    }
}

/* Button Loading State */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button with icons */
.btn i {
    transition: var(--transition-normal);
}

.btn:hover i {
    transform: scale(1.1);
}

.btn-primary:hover i {
    transform: scale(1.1) rotate(5deg);
}

.btn-danger:hover i {
    transform: scale(1.1) rotate(-5deg);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-dark);
    padding: var(--space-2xl) 0 var(--space-xl);
    border-top: 1px solid rgba(247, 147, 26, 0.1);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--bitcoin), transparent);
    opacity: 0.3;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-lg);
}

.footer-left {
    flex: 1;
    min-width: 250px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.footer-logo-icon {
    font-size: 1.5rem;
    color: var(--bitcoin);
    background: rgba(247, 147, 26, 0.1);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-round);
}

.footer-logo-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.footer-description {
    color: var(--text-tertiary);
    margin-bottom: var(--space-md);
    max-width: 400px;
    font-size: var(--text-sm);
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: var(--space-md);
}

.footer-social-link {
    color: var(--text-tertiary);
    font-size: 1.2rem;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-round);
    background: rgba(255, 255, 255, 0.05);
}

.footer-social-link:hover {
    color: var(--bitcoin);
    background: rgba(247, 147, 26, 0.1);
    transform: translateY(-3px);
}

.footer-right {
    display: flex;
    gap: var(--space-2xl);
    flex-wrap: wrap;
}

.footer-links-group {
    min-width: 120px;
}

.footer-links-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--space-md);
    font-size: var(--text-base);
    position: relative;
    display: inline-block;
}

.footer-links-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--bitcoin);
    border-radius: var(--radius-full);
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.footer-link {
    color: var(--text-tertiary);
    font-size: var(--text-sm);
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    padding: var(--space-xs) 0;
}

.footer-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--bitcoin);
    transition: width 0.3s ease;
}

.footer-link:hover {
    color: var(--bitcoin);
    transform: translateX(3px);
}

.footer-link:hover::before {
    width: 100%;
}

.footer-bottom {
    margin-top: var(--space-2xl);
    padding-top: var(--space-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-md);
}

.footer-copyright {
    color: var(--text-tertiary);
    font-size: var(--text-xs);
}

.footer-bottom-links {
    display: flex;
    gap: var(--space-lg);
}

.footer-bottom-link {
    color: var(--text-tertiary);
    font-size: var(--text-xs);
    transition: color 0.3s ease;
}

.footer-bottom-link:hover {
    color: var(--bitcoin);
}

@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .footer-right {
        width: 100%;
        justify-content: space-between;
    }

    .footer-bottom {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: var(--space-md);
    }
}

/* ===== TABLES ===== */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-xl);
    border: 1px solid #2d3748;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: #1a202c;
    position: relative;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

@media (max-width: 992px) {
    .table-container {
        margin-bottom: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .table-container {
        border-radius: var(--radius-lg);
    }
}

@media (max-width: 576px) {
    .table-container {
        border-radius: var(--radius-md);
        margin-bottom: var(--space-md);
    }
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-bitcoin);
    z-index: 1;
}

.table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-bottom: 0;
    background: #1a202c;
    border: none;
}

@media (max-width: 768px) {
    .table {
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }
}

@media (max-width: 576px) {
    .table {
        border-radius: 0 0 var(--radius-md) var(--radius-md);
    }
}

.table th {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 1px;
    padding: 16px;
    text-align: left;
    border-bottom: 2px solid #4a5568;
    position: relative;
    background: #2d3748;
    color: #ffffff;
}

.table th i {
    margin-left: 8px;
    font-size: 0.7rem;
    color: #ffffff;
}

@media (max-width: 768px) {
    .table th {
        padding: var(--space-md);
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .table th {
        padding: var(--space-sm);
        font-size: 0.65rem;
    }
}

.table td {
    padding: 16px;
    border-bottom: 1px solid #4a5568;
    font-size: 0.85rem;
    color: #e2e8f0;
}

@media (max-width: 768px) {
    .table td {
        padding: var(--space-md);
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .table td {
        padding: var(--space-sm);
        font-size: 0.75rem;
    }
}

.table tr {
    position: relative;
    border: none;
}

.table tr:nth-child(odd) td {
    background: #2d3748;
}

.table tr:nth-child(even) td {
    background: #1a202c;
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr.table-row-success {
    border-left: 4px solid var(--success);
}

.table tr.table-row-success td {
    border-bottom: 1px solid rgba(5, 150, 105, 0.2);
}

.table tr.table-row-warning {
    border-left: 4px solid var(--warning);
}

.table tr.table-row-warning td {
    border-bottom: 1px solid rgba(202, 138, 4, 0.2);
}

.table tr.table-row-danger {
    border-left: 4px solid var(--danger);
}

.table tr.table-row-danger td {
    border-bottom: 1px solid rgba(185, 28, 28, 0.2);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #2d3748;
    border-bottom: 1px solid #4a5568;
    margin-bottom: 0;
    position: relative;
}

@media (max-width: 992px) {
    .table-header {
        flex-direction: column;
        gap: var(--space-lg);
        align-items: stretch;
    }
}

@media (max-width: 768px) {
    .table-header {
        padding: var(--space-lg);
        border-radius: var(--radius-md) var(--radius-md) 0 0;
    }
}

@media (max-width: 576px) {
    .table-header {
        padding: var(--space-md);
        gap: var(--space-md);
    }
}

/* Remove table header before and after elements */

.table-filters {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.table-search {
    position: relative;
}

.table-search input {
    padding: 12px 12px 12px 36px;
    background: #1a202c;
    border: 1px solid #4a5568;
    border-radius: 4px;
    color: #ffffff;
    width: 250px;
}

.table-search input:focus {
    border-color: #4a5568;
    outline: none;
}

.table-search i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
}

.table-filter {
    position: relative;
}

.table-filter select {
    padding: 12px 36px 12px 12px;
    background: #1a202c;
    border: 1px solid #4a5568;
    border-radius: 4px;
    color: #ffffff;
    appearance: none;
    width: 150px;
}

.table-filter select:focus {
    border-color: #4a5568;
    outline: none;
}

.table-filter i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    pointer-events: none;
}

.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #2d3748;
    border-top: 1px solid #4a5568;
    margin-top: 0;
    position: relative;
}

@media (max-width: 992px) {
    .table-footer {
        flex-direction: column;
        gap: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .table-footer {
        padding: var(--space-lg);
        border-radius: var(--radius-lg);
    }
}

@media (max-width: 576px) {
    .table-footer {
        padding: var(--space-md);
        gap: var(--space-md);
        border-radius: var(--radius-md);
    }
}

.table-pagination {
    display: flex;
    gap: 8px;
}

.table-pagination button,
.table-pagination a {
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: #1a202c;
    color: #e2e8f0;
    border: 1px solid #4a5568;
}

.table-pagination button.active,
.table-pagination a.active {
    background: #4a5568;
    color: #ffffff;
    border-color: #4a5568;
}

.table-info {
    font-size: 0.9rem;
    color: #a0aec0;
}

.table-info span {
    color: #ffffff;
    font-weight: 700;
}

/* ===== BADGES ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    font-weight: 600;
    gap: var(--space-xs);
}

.badge-primary {
    background: rgba(15, 23, 42, 0.7);
    color: var(--bitcoin);
    border: 1px solid rgba(255, 149, 0, 0.3);
}

.badge-success {
    background: rgba(31, 41, 55, 0.7);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge-danger {
    background: rgba(31, 41, 55, 0.7);
    color: var(--danger);
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.badge-warning {
    background: rgba(31, 41, 55, 0.7);
    color: var(--warning);
    border: 1px solid rgba(217, 119, 6, 0.3);
}

.badge-info {
    background: rgba(31, 41, 55, 0.7);
    color: var(--info);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* ===== ALERTS ===== */
.alert {
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.alert-primary {
    background: rgba(247, 147, 26, 0.1);
    border-left: 4px solid var(--bitcoin);
    color: var(--bitcoin);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-left: 4px solid var(--success);
    color: var(--success);
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    border-left: 4px solid var(--danger);
    color: var(--danger);
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    border-left: 4px solid var(--warning);
    color: var(--warning);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-left: 4px solid var(--info);
    color: var(--info);
}

/* ===== COIN ICONS ===== */
.coin-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--transition-normal);
}

.coin-icon-bitcoin {
    background: linear-gradient(135deg, rgba(255, 149, 0, 0.2), rgba(255, 149, 0, 0.1));
    border: 1px solid rgba(255, 149, 0, 0.3);
    color: var(--bitcoin);
}

.coin-icon-ethereum {
    background: linear-gradient(135deg, rgba(98, 126, 234, 0.2), rgba(98, 126, 234, 0.1));
    border: 1px solid rgba(98, 126, 234, 0.3);
    color: var(--ethereum);
}

.coin-icon-dogecoin {
    background: linear-gradient(135deg, rgba(195, 166, 52, 0.2), rgba(195, 166, 52, 0.1));
    border: 1px solid rgba(195, 166, 52, 0.3);
    color: var(--dogecoin);
}

.coin-name {
    font-weight: 700;
    color: var(--text-primary);
    font-size: var(--text-base);
    text-transform: capitalize;
}

.coin-symbol {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    font-weight: 500;
}

/* ===== CONDITION BADGES ===== */
.condition-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
}

.condition-badge-gt {
    background: rgba(31, 41, 55, 0.7);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.condition-badge-lt {
    background: rgba(31, 41, 55, 0.7);
    color: var(--danger);
    border: 1px solid rgba(220, 38, 38, 0.3);
}

/* ===== STATUS BADGES ===== */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
}

.status-triggered {
    background: rgba(31, 41, 55, 0.7);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-waiting {
    background: rgba(31, 41, 55, 0.7);
    color: var(--info);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-badge-pulse {
    animation: pulse 2s infinite;
}

/* ===== DATE DISPLAY ===== */
.date-display {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.date-primary {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.date-primary i {
    color: var(--text-tertiary);
}

.date-secondary {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin-left: var(--space-lg);
}

/* ===== PRICE DISPLAY IN TABLE ===== */
.price-display {
    font-weight: 700;
    font-size: var(--text-base);
    color: var(--text-primary);
}

.price-currency {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin-top: var(--space-xs);
}

/* ===== PRICE DISPLAY IN TABLE ===== */
.price-display {
    font-weight: 700;
    font-size: var(--text-base);
}

/* ===== MARKET SENTIMENT ===== */
.price-card.sentiment::before {
    background: linear-gradient(to right, #6c5ce7, #00cec9);
}

.market-sentiment {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 10px;
}

.sentiment-value {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
}

.sentiment-indicator {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sentiment-bar {
    height: 100%;
    width: 0;
    transition: width 1s ease, background-color 1s ease;
}

.market-sentiment.bearish .sentiment-value {
    color: #ef4444;
}

.market-sentiment.bearish .sentiment-bar {
    width: 30%;
    background: linear-gradient(to right, #ef4444, #f87171);
}

.market-sentiment.neutral .sentiment-value {
    color: #f59e0b;
}

.market-sentiment.neutral .sentiment-bar {
    width: 50%;
    background: linear-gradient(to right, #f59e0b, #fbbf24);
}

.market-sentiment.bullish .sentiment-value {
    color: #10b981;
}

.market-sentiment.bullish .sentiment-bar {
    width: 80%;
    background: linear-gradient(to right, #10b981, #34d399);
}

.sentiment-description {
    font-size: 0.9rem;
    color: var(--text-tertiary);
    text-align: center;
    margin-top: 8px;
}

/* Light Theme Market Sentiment */
body.light-theme .sentiment-indicator {
    background: rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

body.light-theme .market-sentiment.bearish .sentiment-value {
    color: #dc2626;
}

body.light-theme .market-sentiment.neutral .sentiment-value {
    color: #d97706;
}

body.light-theme .market-sentiment.bullish .sentiment-value {
    color: #059669;
}

body.light-theme .sentiment-description {
    color: #64748b;
}

/* Light Theme Price Cards */
body.light-theme .price-card {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

body.light-theme .price-card h3 {
    color: #475569;
}

body.light-theme .price-card .price-value {
    color: #1e293b;
}

body.light-theme .price-card .price-change.positive {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
}

body.light-theme .price-card .price-change.negative {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
}

body.light-theme .price-card.bitcoin::before {
    opacity: 0.8;
}

body.light-theme .price-card.ethereum::before {
    opacity: 0.8;
}

body.light-theme .price-card.dogecoin::before {
    opacity: 0.8;
}

body.light-theme .price-card.sentiment::before {
    opacity: 0.8;
}

/* Light Theme Quick Actions */
body.light-theme .quick-actions-grid {
    background: #ffffff;
}

body.light-theme .quick-action-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

body.light-theme .quick-action-item:hover {
    background: #ffffff;
    border-color: #cbd5e1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

body.light-theme .quick-action-icon {
    background: rgba(247, 147, 26, 0.1);
    color: #f7931a;
}

body.light-theme .quick-action-text {
    color: #1e293b;
}

body.light-theme .quick-action-description {
    color: #64748b;
}

/* Light Theme Comparison Page */
body.light-theme .comparison-chart {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

body.light-theme .metric-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

body.light-theme .metric-title {
    color: #64748b;
}

body.light-theme .metric-value .coin-symbol {
    color: #64748b;
}

body.light-theme .metric-value .value {
    color: #1e293b;
}

body.light-theme .metric-value.positive .value {
    color: #059669;
}

body.light-theme .metric-value.negative .value {
    color: #dc2626;
}

body.light-theme .correlation-table th,
body.light-theme .correlation-table td {
    border: 1px solid #e2e8f0;
}

body.light-theme .correlation-table th {
    background: #f1f5f9;
    color: #1e293b;
}

body.light-theme .correlation-table td:first-child {
    background: #f1f5f9;
}

body.light-theme .correlation-high {
    background: rgba(5, 150, 105, 0.1);
    color: #059669;
}

body.light-theme .correlation-medium {
    background: rgba(217, 119, 6, 0.1);
    color: #d97706;
}

body.light-theme .correlation-low {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
}

body.light-theme .legend-color.correlation-high {
    background: rgba(5, 150, 105, 0.1);
}

body.light-theme .legend-color.correlation-medium {
    background: rgba(217, 119, 6, 0.1);
}

body.light-theme .legend-color.correlation-low {
    background: rgba(220, 38, 38, 0.1);
}

body.light-theme .legend-text {
    color: #64748b;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
    display: flex;
    gap: var(--space-xs);
    flex-wrap: wrap;
    justify-content: flex-start;
}

.btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
}

/* ===== UTILITY CLASSES ===== */
.d-flex {
    display: flex;
}

.d-inline {
    display: inline;
}

.d-block {
    display: block;
}

.d-inline-block {
    display: inline-block;
}

.d-none {
    display: none;
}

.flex-column {
    flex-direction: column;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.justify-end {
    justify-content: flex-end;
}

.align-center {
    align-items: center;
}

.align-start {
    align-items: flex-start;
}

.align-end {
    align-items: flex-end;
}

.gap-xs {
    gap: var(--space-xs);
}

.gap-sm {
    gap: var(--space-sm);
}

.gap-md {
    gap: var(--space-md);
}

.gap-lg {
    gap: var(--space-lg);
}

.gap-xl {
    gap: var(--space-xl);
}

.mb-xs {
    margin-bottom: var(--space-xs);
}

.mb-sm {
    margin-bottom: var(--space-sm);
}

.mb-md {
    margin-bottom: var(--space-md);
}

.mb-lg {
    margin-bottom: var(--space-lg);
}

.mb-xl {
    margin-bottom: var(--space-xl);
}

.mt-xs {
    margin-top: var(--space-xs);
}

.mt-sm {
    margin-top: var(--space-sm);
}

.mt-md {
    margin-top: var(--space-md);
}

.mt-lg {
    margin-top: var(--space-lg);
}

.mt-xl {
    margin-top: var(--space-xl);
}

/* Dashboard Components */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.dashboard-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.dashboard-title h1 {
    margin: 0;
    font-size: 1.5rem;
    color: #ffffff;
}

.dashboard-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.dashboard-actions-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .dashboard-actions {
        width: 100%;
        justify-content: flex-start;
        margin-top: var(--space-md);
    }
}

@media (max-width: 576px) {
    .dashboard-header {
        margin-bottom: var(--space-lg);
    }

    .dashboard-actions {
        flex-direction: column;
        width: 100%;
    }

    .dashboard-actions a {
        width: 100%;
        text-align: center;
    }
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .stats-container {
        grid-template-columns: 1fr;
        gap: var(--space-md);
        margin-bottom: var(--space-lg);
    }
}

.stat-card {
    background: var(--primary-dark);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 149, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 149, 0, 0.03) 0%, transparent 100%);
    pointer-events: none;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: rgba(255, 149, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--bitcoin);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--space-xs);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
}

.dashboard-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    margin-bottom: 32px;
}

.dashboard-grid > * {
    flex: 1 1 350px;
}

.dashboard-section {
    margin-bottom: 32px;
}

.dashboard-section-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;
}

.dashboard-section-title h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.dashboard-section-title i {
    color: #f7931a;
    font-size: 1.2rem;
}

@media (max-width: 992px) {
    .dashboard-grid {
        flex-direction: column;
    }

    .dashboard-grid > * {
        flex: 1 1 auto;
    }
}

.quick-actions-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.quick-actions-grid > * {
    flex: 1 1 200px;
}

@media (max-width: 768px) {
    .quick-actions-grid > * {
        flex: 1 1 150px;
    }
}

@media (max-width: 576px) {
    .quick-actions-grid {
        flex-direction: column;
    }

    .quick-actions-grid > * {
        flex: 1 1 auto;
    }
}

.quick-action-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px;
    background: #1a202c;
    border-radius: 8px;
    border: 1px solid #4a5568;
    text-decoration: none;
    position: relative;
    transition: transform 0.2s ease;
}

.quick-action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.quick-action-icon {
    min-width: 50px;
    height: 50px;
    border-radius: 8px;
    background: #2d3748;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: #f7931a;
    margin-right: 16px;
}

.quick-action-content {
    display: flex;
    flex-direction: column;
}

.quick-action-text {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.quick-action-description {
    color: #a0aec0;
    font-size: 0.75rem;
}

.price-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.price-list > * {
    flex: 1 1 300px;
}

.price-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #1a202c;
    border-radius: 8px;
    border: 1px solid #4a5568;
    position: relative;
    transition: transform 0.2s ease;
}

.price-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.price-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    border-radius: 4px 0 0 4px;
}

.price-item.bitcoin::before {
    background: #f7931a;
}

.price-item.ethereum::before {
    background: #627eea;
}

.price-item.dogecoin::before {
    background: #c3a634;
}

.price-icon {
    min-width: 50px;
    height: 50px;
    border-radius: 8px;
    background: #2d3748;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    margin-right: 16px;
}

.price-icon-bitcoin {
    color: #f7931a;
}

.price-icon-ethereum {
    color: #627eea;
}

.price-icon-dogecoin {
    color: #c3a634;
}

.price-details {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.coin-name {
    font-weight: 700;
    color: #ffffff;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.coin-symbol {
    font-size: 0.8rem;
    color: #a0aec0;
    margin-left: 4px;
}

.price-value {
    margin-left: auto;
    text-align: right;
}

.current-price {
    font-weight: 700;
    color: #ffffff;
    font-size: 1.2rem;
}

.price-change {
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-top: 4px;
    padding: 2px 8px;
    border-radius: 4px;
}

.price-up {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.price-down {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

@media (max-width: 576px) {
    .price-list {
        flex-direction: column;
    }

    .price-list > * {
        flex: 1 1 auto;
    }
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
}

@media (max-width: 992px) {
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

.feature-item {
    text-align: center;
    padding: var(--space-lg);
    background: rgba(30, 41, 59, 0.6);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 149, 0, 0.1);
}

.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--radius-round);
    background: rgba(255, 149, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--bitcoin);
    margin: 0 auto var(--space-lg);
}

.feature-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.filter-group {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-actions-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.table-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.table-view-options {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.table-view-options span {
    color: var(--text-tertiary);
    font-size: 0.9rem;
    margin-right: var(--space-xs);
}

.pagination-container {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.pagination-info {
    color: var(--text-tertiary);
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: var(--space-xl) var(--space-md);
    background: rgba(30, 41, 59, 0.6);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 149, 0, 0.1);
    margin: var(--space-xl) 0;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-round);
    background: rgba(255, 149, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: var(--bitcoin);
    margin: 0 auto var(--space-lg);
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.empty-state-description {
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto var(--space-xl);
    line-height: 1.6;
}

.empty-state-actions {
    margin-top: var(--space-lg);
}

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: var(--space-md) var(--space-lg);
    background: var(--primary-dark);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: var(--space-md);
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 9999;
    max-width: 400px;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification i {
    font-size: 1.2rem;
}

.notification-info {
    border-left: 4px solid var(--info);
}

.notification-info i {
    color: var(--info);
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-success i {
    color: var(--success);
}

.notification-warning {
    border-left: 4px solid var(--warning);
}

.notification-warning i {
    color: var(--warning);
}

.notification-error {
    border-left: 4px solid var(--danger);
}

.notification-error i {
    color: var(--danger);
}

/* Server-side specific styles */
.server-side-filter-form {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.server-side-note {
    background: #2d3748;
    border-radius: 4px;
    padding: 12px 16px;
    margin-top: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid #4a5568;
}

.server-side-note i {
    color: #a0aec0;
    font-size: 1.2rem;
}

.server-side-note span {
    color: #e2e8f0;
}

.search-submit {
    background: none;
    border: none;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    cursor: pointer;
    padding: 0;
}

.sort-header {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #ffffff;
    text-decoration: none;
    width: 100%;
    justify-content: space-between;
}

.sort-header i {
    color: #a0aec0;
}

.items-per-page-form {
    display: flex;
    align-items: center;
}

/* Table pagination links are already styled with buttons */

/* Remove animations */

.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.8;
}

/* Text utilities */
.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-tertiary {
    color: var(--text-tertiary);
}


.text-success {
    color: var(--success);
}

.text-danger {
    color: var(--danger);
}

.text-warning {
    color: var(--warning);
}

.text-info {
    color: var(--info);
}

.text-bitcoin {
    color: var(--bitcoin);
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.font-normal {
    font-weight: 400;
}

.font-italic {
    font-style: italic;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-capitalize {
    text-transform: capitalize;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(var(--pulse-color, 108, 92, 231), 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--pulse-color, 108, 92, 231), 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(var(--pulse-color, 108, 92, 231), 0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== UTILITIES ===== */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

/* Comparison Page Styles */
.comparison-form .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 20px;
}

.comparison-form .form-group {
    flex: 1 1 200px;
}

.comparison-results {
    margin-top: 24px;
}

.comparison-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.comparison-item {
    flex: 1 1 100%;
}

.comparison-chart {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    background: #1a202c;
    border: 1px solid #4a5568;
}

.comparison-chart img {
    width: 100%;
    height: auto;
    display: block;
}

.comparison-metrics {
    flex: 1 1 100%;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.metric-card {
    background: #1a202c;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #4a5568;
}

.metric-title {
    font-size: 0.9rem;
    color: #a0aec0;
    margin-bottom: 12px;
    font-weight: 600;
}

.metric-comparison {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metric-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-value .coin-symbol {
    font-weight: 600;
    font-size: 0.9rem;
    color: #a0aec0;
}

.metric-value .value {
    font-weight: 700;
    font-size: 1rem;
    color: #ffffff;
}

.metric-value.positive .value {
    color: #10b981;
}

.metric-value.negative .value {
    color: #ef4444;
}

.correlation-matrix {
    overflow-x: auto;
    margin-bottom: 16px;
}

.correlation-table {
    width: 100%;
    border-collapse: collapse;
}

.correlation-table th,
.correlation-table td {
    padding: 12px;
    text-align: center;
    border: 1px solid #4a5568;
}

.correlation-table th {
    background: #2d3748;
    color: #ffffff;
    font-weight: 600;
}

.correlation-table td:first-child {
    font-weight: 600;
    background: #2d3748;
}

.correlation-high {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    font-weight: 700;
}

.correlation-medium {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    font-weight: 700;
}

.correlation-low {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    font-weight: 700;
}

.correlation-legend {
    display: flex;
    gap: 16px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.legend-color.correlation-high {
    background: rgba(16, 185, 129, 0.2);
}

.legend-color.correlation-medium {
    background: rgba(245, 158, 11, 0.2);
}

.legend-color.correlation-low {
    background: rgba(239, 68, 68, 0.2);
}

.legend-text {
    font-size: 0.9rem;
    color: #a0aec0;
}

@media (min-width: 992px) {
    .comparison-item {
        flex: 0 0 60%;
    }

    .comparison-metrics {
        flex: 0 0 35%;
    }
}

@media (max-width: 768px) {
    .comparison-form .form-row {
        flex-direction: column;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }
}

.d-flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.flex-column {
    flex-direction: column;
}

.gap-sm {
    gap: var(--space-sm);
}

.gap-md {
    gap: var(--space-md);
}

.gap-lg {
    gap: var(--space-lg);
}

.mb-sm {
    margin-bottom: var(--space-sm);
}

.mb-md {
    margin-bottom: var(--space-md);
}

.mb-lg {
    margin-bottom: var(--space-lg);
}

.mb-xl {
    margin-bottom: var(--space-xl);
}

.mt-sm {
    margin-top: var(--space-sm);
}

.mt-md {
    margin-top: var(--space-md);
}

.mt-lg {
    margin-top: var(--space-lg);
}

.mt-xl {
    margin-top: var(--space-xl);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .container {
        padding: var(--space-md);
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .table-filters {
        flex-direction: column;
    }

    .btn-group {
        flex-wrap: wrap;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--space-lg);
    }
}

@media (max-width: 576px) {
    .nav {
        gap: var(--space-md);
    }

    .card {
        padding: var(--space-lg);
    }

    .table td, .table th {
        padding: var(--space-md);
    }
}

/* ===== CARDS ===== */
.card {
    background: var(--bg-medium);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--space-xl);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-bitcoin);
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(247, 147, 26, 0.15) 0%, transparent 70%);
    border-radius: 0 0 0 100%;
    opacity: 0.5;
}

.card-header {
    margin-bottom: var(--space-lg);
    position: relative;
}

.card-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
    position: relative;
    display: inline-block;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--bitcoin);
    border-radius: var(--radius-full);
}

.card-subtitle {
    color: var(--text-tertiary);
    font-size: var(--text-sm);
    margin-top: var(--space-sm);
}

.card-body {
    position: relative;
    z-index: 1;
}

.card-footer {
    margin-top: var(--space-lg);
    padding-top: var(--space-md);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* Card Variants */
.card-primary {
    border-left: 3px solid var(--bitcoin);
}

.card-primary::before {
    background: var(--gradient-bitcoin);
}

.card-secondary {
    border-left: 3px solid var(--secondary);
}

.card-secondary::before {
    background: var(--gradient-secondary);
}

.card-success {
    border-left: 3px solid var(--success);
}

.card-success::before {
    background: var(--gradient-success);
}

.card-danger {
    border-left: 3px solid var(--danger);
}

.card-danger::before {
    background: var(--gradient-danger);
}

/* Card Sizes */
.card-sm {
    padding: var(--space-md);
}

.card-lg {
    padding: var(--space-2xl);
}

/* Card with Icon */
.card-with-icon {
    display: flex;
    gap: var(--space-lg);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    flex-shrink: 0;
}

.card-icon-primary {
    background: rgba(247, 147, 26, 0.1);
    color: var(--bitcoin);
}

.card-icon-secondary {
    background: rgba(136, 84, 208, 0.1);
    color: var(--secondary);
}

.card-icon-success {
    background: rgba(0, 168, 84, 0.1);
    color: var(--success);
}

.card-icon-danger {
    background: rgba(207, 19, 34, 0.1);
    color: var(--danger);
}

.card-content {
    flex: 1;
}

/* Card with Hover Effect */
.card-hover {
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Card with Glass Effect */
.card-glass {
    background: rgba(31, 41, 55, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== PRICE DISPLAY ===== */
.price-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.price-card {
    position: relative;
    background: var(--bg-medium);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 200px;
}

.price-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
}

.price-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
    pointer-events: none;
}

.price-card:hover::after {
    opacity: 1;
}

.price-card.bitcoin::before {
    background: linear-gradient(to right, var(--bitcoin-dark), var(--bitcoin), var(--bitcoin-light));
}

.price-card.ethereum::before {
    background: linear-gradient(to right, #3c3c9e, var(--ethereum), #a5b9f6);
}

.price-card.dogecoin::before {
    background: linear-gradient(to right, #9e8b2c, var(--dogecoin), #f0e68c);
}

.price-card h3 {
    display: flex;
    align-items: center;
    font-size: var(--text-xl);
    font-weight: 700;
    margin-bottom: var(--space-lg);
    position: relative;
    z-index: 1;
}

.price-card h3 i {
    margin-right: var(--space-sm);
    font-size: 1.2em;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.price-card.bitcoin h3 {
    color: var(--bitcoin);
}

.price-card.ethereum h3 {
    color: var(--ethereum);
}

.price-card.dogecoin h3 {
    color: var(--dogecoin);
}

.price-value {
    font-size: var(--text-4xl);
    font-weight: 800;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
    position: relative;
    z-index: 1;
    letter-spacing: -1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.price-change {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-sm);
    position: relative;
    z-index: 1;
    gap: var(--space-xs);
}

.price-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.price-change.negative {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

/* Crypto-specific card styles */
.price-card.bitcoin {
    background: linear-gradient(135deg,
        rgba(247, 147, 26, 0.1) 0%,
        rgba(31, 41, 55, 1) 100%);
}

.price-card.ethereum {
    background: linear-gradient(135deg,
        rgba(98, 126, 234, 0.1) 0%,
        rgba(31, 41, 55, 1) 100%);
}

.price-card.dogecoin {
    background: linear-gradient(135deg,
        rgba(195, 166, 52, 0.1) 0%,
        rgba(31, 41, 55, 1) 100%);
}

/* Animated price cards */
@keyframes floatUp {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0);
    }
}

.price-card.bitcoin {
    animation: floatUp 6s ease-in-out infinite;
    animation-delay: 0s;
}

.price-card.ethereum {
    animation: floatUp 6s ease-in-out infinite;
    animation-delay: 2s;
}

.price-card.dogecoin {
    animation: floatUp 6s ease-in-out infinite;
    animation-delay: 4s;
}

@media (max-width: 992px) {
    .price-display {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .price-value {
        font-size: var(--text-3xl);
    }
}

@media (max-width: 576px) {
    .price-display {
        grid-template-columns: 1fr;
    }

    .price-card {
        padding: var(--space-lg);
    }

    .price-value {
        font-size: var(--text-2xl);
    }
}

/* Volatility Circle */
.volatility-circle {
    width: 120px;
    height: 120px;
    border-radius: var(--radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    font-weight: 800;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.volatility-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.volatility-circle:hover::before {
    opacity: 1;
}

.volatility-high {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.2) 0%, rgba(220, 38, 38, 0.4) 100%);
    color: var(--danger);
    border: 2px solid rgba(220, 38, 38, 0.3);
}

.volatility-medium {
    background: linear-gradient(135deg, rgba(217, 119, 6, 0.2) 0%, rgba(217, 119, 6, 0.4) 100%);
    color: var(--warning);
    border: 2px solid rgba(217, 119, 6, 0.3);
}

.volatility-low {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.4) 100%);
    color: var(--success);
    border: 2px solid rgba(16, 185, 129, 0.3);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.volatility-high {
    animation: pulse 2s infinite;
}
