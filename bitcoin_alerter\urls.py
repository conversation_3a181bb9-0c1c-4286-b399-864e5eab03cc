"""
URL configuration for bitcoin_alerter project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from crypto_alerter import views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.home, name='home'),
    path('trigger/', views.trigger, name='trigger'),
    path('history/', views.history, name='history'),
    path('charts/', views.charts, name='charts'),
    path('comparison/', views.comparison, name='comparison'),
    path('download/<str:file_id>/', views.download, name='download'),
    path('export-csv/', views.export_csv, name='export_csv'),
    path('news/', views.news, name='news'),
    path('tools/converter/', views.converter, name='converter'),
    path('tools/calculator/', views.calculator, name='calculator'),
    path('calendar/', views.calendar, name='calendar'),
    path('test-api/', views.test_api, name='test_api'),
    path('test-sms/', views.test_sms, name='test_sms'),
    path('delete-alert/<str:alert_id>/', views.delete_alert, name='delete_alert'),
    path('check_sms_notifications/', views.check_sms_notifications, name='check_sms_notifications'),
    path('notifications/', views.notifications, name='notifications'),
    path('api/notifications/', views.notification_api, name='notification_api'),
    path('api/notification-settings/', views.notification_settings, name='notification_settings'),
]
