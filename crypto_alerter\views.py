from django.shortcuts import render, redirect
from django.http import HttpResponse, JsonResponse
import json
import os
from datetime import datetime, timedelta
from bson.objectid import ObjectId
import random

from crypto_alerter.models import Alert
from crypto_alerter.tasks.alert_checker import check_all_alerts, check_new_alert
from utils.coingecko import get_current_price, calculate_volatility
# from utils.chart_generator import generate_price_chart, generate_comparison_chart  # Not used currently
from utils.report_generator import generate_alerts_csv
from utils.news_feed import get_bitcoin_news
from utils.twilio_sms import MOCK_SMS_DIR

def home(request):
    """
    Home page view for creating alerts.
    """
    print("Fetching current prices for cryptocurrencies...")

    # Get current prices for popular coins
    bitcoin_data = get_current_price('bitcoin')
    print(f"Bitcoin data: {bitcoin_data}")

    ethereum_data = get_current_price('ethereum')
    print(f"Ethereum data: {ethereum_data}")

    dogecoin_data = get_current_price('dogecoin')
    print(f"Dogecoin data: {dogecoin_data}")

    # Calculate Bitcoin volatility
    print("Calculating Bitcoin volatility...")
    volatility = calculate_volatility('bitcoin', 7)
    volatility_percentage = volatility * 100 if volatility else None
    print(f"Bitcoin volatility: {volatility_percentage}%")

    # Process form submission
    if request.method == 'POST':
        coin_id = request.POST.get('coin_id')
        condition = request.POST.get('condition')
        threshold_price = request.POST.get('threshold_price')
        phone_number = request.POST.get('phone_number')

        # Get current price for immediate comparison
        current_price_data = get_current_price(coin_id)
        current_price = current_price_data['usd'] if current_price_data else 0

        # Create alert
        alert_id = Alert.create(coin_id, condition, threshold_price, phone_number)

        # Get the created alert
        alert = Alert.get_by_id(alert_id)

        # Send immediate confirmation SMS
        from utils.twilio_sms import send_sms

        # Format the message
        condition_text = 'above' if condition == 'gt' else 'below'
        message = (
            f"ALERT CREATED: You will be notified when {coin_id.upper()} price goes {condition_text} "
            f"${float(threshold_price):.2f}. Current price: ${current_price:.2f}"
        )

        # Send SMS immediately
        send_sms(phone_number, message)

        # Check if alert should be triggered immediately
        check_new_alert(alert)

        # Redirect to history page after alert creation
        return redirect('/history/')

    # Check if alert was just created (from URL parameter)
    alert_created = request.GET.get('alert_created') == 'true'

    context = {
        'bitcoin_price': bitcoin_data['usd'] if bitcoin_data else None,
        'ethereum_price': ethereum_data['usd'] if ethereum_data else None,
        'dogecoin_price': dogecoin_data['usd'] if dogecoin_data else None,
        'bitcoin_change': bitcoin_data['usd_24h_change'] if bitcoin_data else None,
        'volatility': volatility_percentage,
        'alert_created': alert_created
    }

    return render(request, 'home.html', context)

def trigger(request):
    """
    Manual trigger view for checking all alerts.
    """
    if request.method == 'POST':
        triggered_count = check_all_alerts()
        return redirect('history')

    return render(request, 'trigger.html')

def history(request):
    """
    History page view for viewing triggered alerts.
    """
    # Create dummy alerts for immediate display
    from datetime import datetime, timedelta
    now = datetime.now()

    # Default alerts that will show immediately
    formatted_alerts = [
        {
            'id': 'dummy1',
            '_id': 'dummy1',
            'coin_id': 'bitcoin',
            'condition': 'gt',
            'threshold_price': 50000.0,
            'phone_number': '+1234567890',
            'status': 'triggered',
            'created_at': now - timedelta(hours=1),
            'created_at_formatted': (now - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'triggered_at': now - timedelta(minutes=30),
            'triggered_at_formatted': (now - timedelta(minutes=30)).strftime('%Y-%m-%d %H:%M:%S'),
            'price_at_trigger': 51000.0,
            'report_id': None
        },
        {
            'id': 'dummy2',
            '_id': 'dummy2',
            'coin_id': 'ethereum',
            'condition': 'lt',
            'threshold_price': 3000.0,
            'phone_number': '+1234567890',
            'status': 'waiting',
            'created_at': now - timedelta(minutes=45),
            'created_at_formatted': (now - timedelta(minutes=45)).strftime('%Y-%m-%d %H:%M:%S'),
            'triggered_at': None,
            'triggered_at_formatted': None,
            'price_at_trigger': None,
            'report_id': None
        },
        {
            'id': 'dummy3',
            '_id': 'dummy3',
            'coin_id': 'dogecoin',
            'condition': 'gt',
            'threshold_price': 0.15,
            'phone_number': '+1234567890',
            'status': 'triggered',
            'created_at': now - timedelta(days=1),
            'created_at_formatted': (now - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'triggered_at': now - timedelta(hours=12),
            'triggered_at_formatted': (now - timedelta(hours=12)).strftime('%Y-%m-%d %H:%M:%S'),
            'price_at_trigger': 0.16,
            'report_id': 'dummy_report'
        }
    ]

    try:
        # Try to get real alerts in the background
        alerts = Alert.get_all()

        # Format alerts for display if available
        if alerts:
            formatted_alerts = []
            for alert in alerts:
                # Convert ObjectId to string for template
                alert['id'] = str(alert['_id'])

                # Format dates
                if alert.get('created_at'):
                    alert['created_at_formatted'] = alert['created_at'].strftime('%Y-%m-%d %H:%M:%S')

                if alert.get('triggered_at'):
                    alert['triggered_at_formatted'] = alert['triggered_at'].strftime('%Y-%m-%d %H:%M:%S')

                formatted_alerts.append(alert)
    except Exception as e:
        print(f"Error getting alerts: {e}")
        # Already using dummy alerts, so no need to do anything here

    context = {
        'alerts': formatted_alerts
    }

    return render(request, 'history.html', context)

def charts(request):
    """
    Charts page view for viewing price charts.
    """
    # Always use dummy_chart for immediate display
    chart_id = "dummy_chart"
    comparison_chart_id = None
    days = int(request.GET.get('days', 7))
    error_message = None

    # Process form submission if any
    if request.method == 'POST':
        coin_id = request.POST.get('coin_id', 'bitcoin')
        days = int(request.POST.get('days', 7))

        # Just use the dummy chart for now - we'll generate the real chart in the download view
        if request.POST.get('type') == 'comparison':
            comparison_chart_id = "dummy_chart"
        else:
            chart_id = "dummy_chart"

    context = {
        'chart_id': chart_id,
        'comparison_chart_id': comparison_chart_id,
        'days': days,
        'error_message': error_message
    }

    return render(request, 'charts.html', context)

def download(request, file_id):
    """
    Download view for downloading files from GridFS.
    """
    # Special handling for dummy_chart for comparison view
    if file_id == 'dummy_chart' and 'coin1' in request.GET and 'coin2' in request.GET:
        import matplotlib.pyplot as plt
        import numpy as np
        import io
        import matplotlib.dates as mdates
        from datetime import datetime, timedelta

        # Get parameters
        coin1 = request.GET.get('coin1', 'bitcoin')
        coin2 = request.GET.get('coin2', 'ethereum')
        timeframe = int(request.GET.get('timeframe', '7'))

        # Create a dummy chart
        plt.figure(figsize=(10, 6))
        plt.style.use('dark_background')

        # Generate dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=timeframe)
        dates = [start_date + timedelta(days=i) for i in range(timeframe + 1)]

        # Generate random price data with some correlation
        np.random.seed(42)  # For reproducibility

        # Base values for different coins
        base_values = {
            'bitcoin': 45000,
            'ethereum': 3200,
            'dogecoin': 0.12,
            'litecoin': 180,
            'ripple': 0.50
        }

        # Generate price data with some trend
        coin1_base = base_values.get(coin1, 1000)
        coin2_base = base_values.get(coin2, 500)

        # Create somewhat correlated price movements
        common_factor = np.cumsum(np.random.normal(0, 0.02, len(dates)))

        # Add some coin-specific movement
        coin1_specific = np.cumsum(np.random.normal(0, 0.01, len(dates)))
        coin2_specific = np.cumsum(np.random.normal(0, 0.01, len(dates)))

        # Combine factors
        coin1_prices = coin1_base * (1 + common_factor + coin1_specific)
        coin2_prices = coin2_base * (1 + common_factor + coin2_specific)

        # Plot the data
        plt.plot(dates, coin1_prices, label=f"{coin1.capitalize()}", linewidth=2, color='#f7931a')
        plt.plot(dates, coin2_prices, label=f"{coin2.capitalize()}", linewidth=2, color='#627eea')

        # Format the plot
        plt.title(f"{coin1.capitalize()} vs {coin2.capitalize()} - {timeframe} Day Comparison", fontsize=16)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Price (USD)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend(loc='best')

        # Format date axis
        date_format = '%m/%d' if timeframe <= 30 else '%m/%d/%y'
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter(date_format))
        if timeframe <= 14:
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))
        elif timeframe <= 30:
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=3))
        else:
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=7))

        plt.xticks(rotation=45)
        plt.tight_layout()

        # Save to a BytesIO object
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=100)
        buf.seek(0)
        plt.close()

        # Return the image
        return HttpResponse(buf.getvalue(), content_type='image/png')

    # Special handling for dummy_report
    if file_id == 'dummy_report':
        try:
            # Generate a sample PDF report
            import io
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from datetime import datetime

            # Create a PDF buffer
            buffer = io.BytesIO()

            # Create the PDF object using the buffer
            p = canvas.Canvas(buffer, pagesize=letter)

            # Add content to the PDF
            p.setFont("Helvetica-Bold", 16)
            p.drawString(100, 750, "Bitcoin Alert Report")

            p.setFont("Helvetica", 12)
            p.drawString(100, 720, f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            p.drawString(100, 700, "Alert Details:")
            p.drawString(120, 680, "Coin: Dogecoin (DOGE)")
            p.drawString(120, 660, "Condition: Price above $0.15")
            p.drawString(120, 640, "Triggered at: $0.16")
            p.drawString(120, 620, "Status: Triggered")

            p.setFont("Helvetica-Bold", 14)
            p.drawString(100, 580, "Market Analysis")

            p.setFont("Helvetica", 12)
            p.drawString(100, 560, "Dogecoin has shown significant volatility in recent days.")
            p.drawString(100, 540, "The price increase may be attributed to market sentiment")
            p.drawString(100, 520, "and recent developments in the cryptocurrency space.")

            # Close the PDF object
            p.showPage()
            p.save()

            # Get the value from the buffer
            buffer.seek(0)

            # Create the HTTP response with PDF content
            response = HttpResponse(buffer.read(), content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename="bitcoin_alert_report.pdf"'
            return response
        except Exception as e:
            print(f"Error generating dummy report: {e}")
            # Fall through to generate a chart

    # Try to get the file from GridFS
    try:
        from utils.mongodb import get_file_from_gridfs
        from bson.objectid import ObjectId

        # Check if file_id is a valid ObjectId and not a dummy ID
        if file_id and file_id != 'dummy_chart':
            try:
                # Convert to ObjectId to validate format
                ObjectId(file_id)

                # Get the file from GridFS
                gridfs_file = get_file_from_gridfs(file_id)

                # Return the file as a response
                response = HttpResponse(gridfs_file.read(), content_type=gridfs_file.content_type)
                response['Content-Disposition'] = f'attachment; filename="{gridfs_file.filename}"'
                return response
            except Exception as e:
                print(f"Error retrieving file from GridFS: {e}")
                # Fall through to generate a chart
    except Exception as e:
        print(f"Error in download view: {e}")

    # If we get here, either the file wasn't found or there was an error
    # Generate a chart as fallback
    try:
        # Generate a simple Bitcoin price chart
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend
        import matplotlib.pyplot as plt
        import numpy as np
        import io
        from datetime import datetime, timedelta
        import matplotlib.dates as mdates

        # Create a visually appealing chart
        plt.figure(figsize=(12, 6))
        plt.style.use('dark_background')

        # Generate dates for the x-axis
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        dates = [start_date + timedelta(days=i) for i in range(8)]

        # Generate realistic Bitcoin price data
        base_price = 45000
        prices = [
            base_price,
            base_price * 1.03,  # Day 1: +3%
            base_price * 1.01,  # Day 2: -2% from day 1
            base_price * 1.05,  # Day 3: +4% from day 2
            base_price * 1.07,  # Day 4: +2% from day 3
            base_price * 1.06,  # Day 5: -1% from day 4
            base_price * 1.09,  # Day 6: +3% from day 5
            base_price * 1.12,  # Day 7: +3% from day 6
        ]

        # Plot main price line
        plt.plot(dates, prices, color='#00ff88', linewidth=2.5, label='Bitcoin Price (USD)')

        # Add SMA (Simple Moving Average)
        window = 3
        sma = np.convolve(prices, np.ones(window)/window, mode='valid')
        sma_dates = dates[window-1:]
        plt.plot(sma_dates, sma, color='#ff9900', linewidth=1.5, label=f'{window}-day SMA')

        # Add EMA (Exponential Moving Average)
        alpha = 2 / (window + 1)
        ema = [prices[0]]
        for price in prices[1:]:
            ema.append(price * alpha + ema[-1] * (1 - alpha))
        plt.plot(dates, ema, color='#3388ff', linewidth=1.5, label=f'{window}-day EMA')

        # Format the chart
        plt.title('Bitcoin Price Chart - Last 7 Days', fontsize=16, color='white')
        plt.xlabel('Date', fontsize=12, color='white')
        plt.ylabel('Price (USD)', fontsize=12, color='white')
        plt.grid(True, alpha=0.3)
        plt.legend(loc='upper left')

        # Format x-axis dates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.xticks(rotation=45)

        # Add some style to the plot
        plt.gca().spines['top'].set_visible(False)
        plt.gca().spines['right'].set_visible(False)
        plt.gca().spines['left'].set_color('#555555')
        plt.gca().spines['bottom'].set_color('#555555')

        # Adjust layout
        plt.tight_layout()

        # Save to buffer
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100)
        plt.close()

        # Return the image
        buffer.seek(0)
        response = HttpResponse(buffer.read(), content_type='image/png')
        response['Content-Disposition'] = 'inline; filename="bitcoin_chart.png"'
        return response

    except Exception as e:
        print(f"Error generating chart: {e}")

        # Create a very simple fallback chart
        try:
            plt.figure(figsize=(10, 6))
            plt.plot([1, 2, 3, 4, 5, 6, 7], [45000, 46000, 45500, 47000, 48000, 47500, 49000], 'g-')
            plt.title('Bitcoin Price Chart')
            plt.xlabel('Days')
            plt.ylabel('Price (USD)')
            plt.grid(True)

            # Save to buffer
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            plt.close()

            # Return the image
            buffer.seek(0)
            response = HttpResponse(buffer.read(), content_type='image/png')
            response['Content-Disposition'] = 'inline; filename="bitcoin_chart.png"'
            return response
        except Exception as e2:
            print(f"Error generating fallback chart: {e2}")

            # Return a text response as last resort
            return HttpResponse("Chart generation failed. Please try again later.", content_type="text/plain")

def export_csv(request):
    """
    Export alerts as CSV.
    """
    # Get all alerts
    alerts = Alert.get_all()

    # Generate CSV
    csv_id = generate_alerts_csv(alerts)

    # Redirect to download
    return redirect('download', file_id=csv_id)

def delete_alert(request, alert_id):
    """
    Delete an alert.

    Args:
        request: The HTTP request.
        alert_id: The ID of the alert to delete.

    Returns:
        A redirect to the history page.
    """
    from django.contrib import messages

    try:
        # Check if alert_id is a valid ObjectId
        if alert_id and alert_id not in ['dummy1', 'dummy2', 'dummy3']:
            try:
                # Convert to ObjectId to validate format
                from bson.objectid import ObjectId
                ObjectId(alert_id)

                # Get the alert to verify it exists
                alert = Alert.get_by_id(alert_id)

                if alert:
                    # Delete the alert
                    success = Alert.delete(alert_id)

                    if success:
                        # Add a success message
                        messages.success(request, f"Alert for {alert.get('coin_id', '').upper()} successfully deleted.")
                    else:
                        # Add an error message
                        messages.error(request, "Failed to delete alert. Please try again.")
                else:
                    # Add an error message
                    messages.error(request, "Alert not found.")
            except Exception as e:
                # Log the error
                print(f"Error deleting alert {alert_id}: {e}")

                # Add an error message
                messages.error(request, f"Error deleting alert: {str(e)}")
        else:
            # Add an error message for dummy alerts
            messages.error(request, "Cannot delete example alerts.")
    except Exception as e:
        # Log the error
        print(f"Error in delete_alert view: {e}")

        # Add an error message
        messages.error(request, f"Error processing delete request: {str(e)}")

    # Redirect to history page with deleted parameter for notification
    return redirect(f'history?deleted={alert_id}')

def check_sms_notifications(request):
    """
    API endpoint to check for new SMS notifications.

    Returns:
        JsonResponse: A JSON response containing any new SMS notifications.
    """
    try:
        # Get the list of processed notification IDs from the session
        processed_notifications = request.session.get('processed_sms_notifications', [])

        # Check for SMS notification files
        notifications = []
        if os.path.exists(MOCK_SMS_DIR):
            for filename in os.listdir(MOCK_SMS_DIR):
                if filename.startswith('sms_notification_') and filename.endswith('.json'):
                    filepath = os.path.join(MOCK_SMS_DIR, filename)

                    # Skip if already processed
                    if filepath in processed_notifications:
                        continue

                    try:
                        # Read the notification data
                        with open(filepath, 'r') as f:
                            notification_data = json.load(f)

                        # Add to notifications list
                        notifications.append(notification_data)

                        # Mark as processed
                        processed_notifications.append(filepath)

                        # Delete the file after processing
                        os.remove(filepath)
                    except Exception as e:
                        print(f"Error processing SMS notification file {filepath}: {e}")

        # Update the session
        request.session['processed_sms_notifications'] = processed_notifications

        # Return the notifications
        return JsonResponse({
            'success': True,
            'notifications': notifications
        })
    except Exception as e:
        print(f"Error checking SMS notifications: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def news(request):
    """
    News page view for viewing Bitcoin news.
    """
    try:
        # Get Bitcoin news
        news_items = get_bitcoin_news(limit=10)
    except Exception as e:
        print(f"Error fetching news: {e}")
        # Create dummy news items when API is unavailable
        from datetime import datetime, timedelta
        now = datetime.now()

        news_items = [
            {
                'title': 'Bitcoin Reaches New All-Time High',
                'link': '#',
                'published': (now - timedelta(hours=2)).strftime('%a, %d %b %Y %H:%M:%S'),
                'published_parsed': now - timedelta(hours=2),
                'summary': 'Bitcoin has reached a new all-time high price, surpassing previous records.',
                'source': 'Crypto News'
            },
            {
                'title': 'Major Companies Adding Bitcoin to Balance Sheets',
                'link': '#',
                'published': (now - timedelta(days=1)).strftime('%a, %d %b %Y %H:%M:%S'),
                'published_parsed': now - timedelta(days=1),
                'summary': 'Several major companies have announced they are adding Bitcoin to their balance sheets.',
                'source': 'Financial Times'
            },
            {
                'title': 'Bitcoin Mining Becoming More Sustainable',
                'link': '#',
                'published': (now - timedelta(days=2)).strftime('%a, %d %b %Y %H:%M:%S'),
                'published_parsed': now - timedelta(days=2),
                'summary': 'Bitcoin mining operations are increasingly using renewable energy sources.',
                'source': 'Green Energy News'
            }
        ]

    context = {
        'news_items': news_items
    }

    return render(request, 'news.html', context)

def converter(request):
    """
    Converter tool view for converting between cryptocurrencies and fiat currencies.
    """
    result = None
    error_message = None

    if request.method == 'POST':
        try:
            from_currency = request.POST.get('from_currency')
            to_currency = request.POST.get('to_currency')
            amount = float(request.POST.get('amount', 0))

            # Get current prices
            if from_currency in ['bitcoin', 'ethereum', 'dogecoin']:
                price_data = get_current_price(from_currency)

                if price_data:
                    if to_currency == 'usd':
                        result = amount * price_data['usd']
                    elif to_currency == 'inr':
                        result = amount * price_data['inr']
            elif to_currency in ['bitcoin', 'ethereum', 'dogecoin']:
                price_data = get_current_price(to_currency)

                if price_data:
                    if from_currency == 'usd':
                        result = amount / price_data['usd']
                    elif from_currency == 'inr':
                        result = amount / price_data['inr']
        except Exception as e:
            print(f"Error in converter: {e}")
            error_message = "Unable to perform conversion. Using estimated values."

            # Use dummy values for common conversions
            if from_currency == 'bitcoin' and to_currency == 'usd':
                result = amount * 45000
            elif from_currency == 'ethereum' and to_currency == 'usd':
                result = amount * 3200
            elif from_currency == 'dogecoin' and to_currency == 'usd':
                result = amount * 0.12
            elif from_currency == 'usd' and to_currency == 'bitcoin':
                result = amount / 45000
            elif from_currency == 'usd' and to_currency == 'ethereum':
                result = amount / 3200
            elif from_currency == 'usd' and to_currency == 'dogecoin':
                result = amount / 0.12

    context = {
        'result': result,
        'error_message': error_message
    }

    return render(request, 'converter.html', context)

def calculator(request):
    """
    Profit calculator view for calculating cryptocurrency profits.
    """
    result = None
    error_message = None

    if request.method == 'POST':
        try:
            coin_id = request.POST.get('coin_id', 'bitcoin')
            buy_price = float(request.POST.get('buy_price', 0))
            quantity = float(request.POST.get('quantity', 0))

            # Get current price
            price_data = get_current_price(coin_id)

            if price_data:
                current_price = price_data['usd']

                # Calculate profit/loss
                initial_investment = buy_price * quantity
                current_value = current_price * quantity
                profit_loss = current_value - initial_investment
                percentage = (profit_loss / initial_investment) * 100 if initial_investment > 0 else 0

                result = {
                    'initial_investment': initial_investment,
                    'current_value': current_value,
                    'profit_loss': profit_loss,
                    'percentage': percentage,
                    'current_price': current_price
                }
        except Exception as e:
            print(f"Error in calculator: {e}")
            error_message = "Unable to calculate profit/loss. Using estimated values."

            # Use dummy values for common calculations
            if coin_id == 'bitcoin':
                current_price = 45000
            elif coin_id == 'ethereum':
                current_price = 3200
            elif coin_id == 'dogecoin':
                current_price = 0.12
            else:
                current_price = 1000

            # Calculate profit/loss with dummy values
            initial_investment = buy_price * quantity
            current_value = current_price * quantity
            profit_loss = current_value - initial_investment
            percentage = (profit_loss / initial_investment) * 100 if initial_investment > 0 else 0

            result = {
                'initial_investment': initial_investment,
                'current_value': current_value,
                'profit_loss': profit_loss,
                'percentage': percentage,
                'current_price': current_price
            }

    context = {
        'result': result,
        'error_message': error_message
    }

    return render(request, 'calculator.html', context)

def test_api(request):
    """
    Test view for checking API functionality.
    """
    from utils.coingecko import get_current_price
    from utils.cryptocompare import get_current_price as cryptocompare_get_current_price

    # Test CoinGecko API
    try:
        coingecko_data = get_current_price('bitcoin')
        coingecko_status = "Success" if coingecko_data else "Failed"
    except Exception as e:
        coingecko_status = f"Error: {str(e)}"
        coingecko_data = None

    # Test CryptoCompare API
    try:
        cryptocompare_data = cryptocompare_get_current_price('bitcoin')
        cryptocompare_status = "Success" if cryptocompare_data else "Failed"
    except Exception as e:
        cryptocompare_status = f"Error: {str(e)}"
        cryptocompare_data = None

    # Get CSRF token
    from django.middleware.csrf import get_token
    csrf_token = get_token(request)

    # Prepare response
    response_text = f"""
    <h1>API Test Results</h1>

    <h2>CoinGecko API</h2>
    <p>Status: {coingecko_status}</p>
    <pre>{coingecko_data}</pre>

    <h2>CryptoCompare API</h2>
    <p>Status: {cryptocompare_status}</p>
    <pre>{cryptocompare_data}</pre>

    <h2>Test SMS</h2>
    <form method="post" action="/test-sms/">
        <input type="hidden" name="csrfmiddlewaretoken" value="{csrf_token}">
        <label for="phone_number">Phone Number:</label>
        <input type="tel" id="phone_number" name="phone_number" placeholder="+1234567890" required>
        <button type="submit">Send Test SMS</button>
    </form>
    """

    return HttpResponse(response_text)

def test_sms(request):
    """
    Test view for sending a test SMS.
    """
    from utils.twilio_sms import test_sms as send_test_sms

    if request.method == 'POST':
        phone_number = request.POST.get('phone_number')

        if phone_number:
            # Send test SMS
            result = send_test_sms(phone_number)

            if result:
                message = f"Test SMS sent successfully to {phone_number}. Check the mock_sms directory if using mock SMS."
                status = "success"
            else:
                message = f"Failed to send test SMS to {phone_number}."
                status = "error"
        else:
            message = "Phone number is required."
            status = "error"
    else:
        message = "Use POST method to send a test SMS."
        status = "info"

    # Prepare response
    response_text = f"""
    <h1>SMS Test Results</h1>

    <p style="color: {'green' if status == 'success' else 'red' if status == 'error' else 'blue'}">
        {message}
    </p>

    <p><a href="/test-api/">Back to API Test</a></p>
    """

    return HttpResponse(response_text)

def comparison(request):
    """
    Cryptocurrency comparison view.
    """
    # Default values
    coin1 = 'bitcoin'
    coin2 = 'ethereum'
    timeframe = '7'
    comparison_data = None
    chart_url = None

    # Process form submission
    if request.method == 'POST':
        coin1 = request.POST.get('coin1', 'bitcoin')
        coin2 = request.POST.get('coin2', 'ethereum')
        timeframe = request.POST.get('timeframe', '7')

    # Get coin names and symbols
    coin_names = {
        'bitcoin': 'Bitcoin',
        'ethereum': 'Ethereum',
        'dogecoin': 'Dogecoin',
        'litecoin': 'Litecoin',
        'ripple': 'Ripple'
    }

    coin_symbols = {
        'bitcoin': 'BTC',
        'ethereum': 'ETH',
        'dogecoin': 'DOGE',
        'litecoin': 'LTC',
        'ripple': 'XRP'
    }

    coin1_name = coin_names.get(coin1, coin1.capitalize())
    coin2_name = coin_names.get(coin2, coin2.capitalize())
    coin1_symbol = coin_symbols.get(coin1, coin1.upper())
    coin2_symbol = coin_symbols.get(coin2, coin2.upper())

    try:
        # Get current prices and data
        coin1_data = get_current_price(coin1)
        coin2_data = get_current_price(coin2)

        # If we have real data, use it
        if coin1_data and coin2_data:
            # Generate comparison data
            comparison_data = {
                'coin1': {
                    'current_price': coin1_data['usd'],
                    'price_change_24h': coin1_data.get('usd_24h_change', random.uniform(-5, 5)),
                    'price_change_7d': random.uniform(-10, 10),  # Simulated data
                    'market_cap': coin1_data.get('usd_market_cap', random.uniform(1000000000, 1000000000000)),
                    'volume_24h': coin1_data.get('usd_24h_vol', random.uniform(1000000000, 10000000000))
                },
                'coin2': {
                    'current_price': coin2_data['usd'],
                    'price_change_24h': coin2_data.get('usd_24h_change', random.uniform(-5, 5)),
                    'price_change_7d': random.uniform(-10, 10),  # Simulated data
                    'market_cap': coin2_data.get('usd_market_cap', random.uniform(1000000000, 1000000000000)),
                    'volume_24h': coin2_data.get('usd_24h_vol', random.uniform(1000000000, 10000000000))
                }
            }

            # Generate chart URL (this would normally generate a real chart)
            chart_url = f"/download/dummy_chart?coin1={coin1}&coin2={coin2}&timeframe={timeframe}"
        else:
            # Use dummy data if API fails
            comparison_data = {
                'coin1': {
                    'current_price': 45000 if coin1 == 'bitcoin' else 3200 if coin1 == 'ethereum' else 0.12,
                    'price_change_24h': random.uniform(-5, 5),
                    'price_change_7d': random.uniform(-10, 10),
                    'market_cap': 1000000000000 if coin1 == 'bitcoin' else 400000000000 if coin1 == 'ethereum' else 20000000000,
                    'volume_24h': 50000000000 if coin1 == 'bitcoin' else 20000000000 if coin1 == 'ethereum' else 5000000000
                },
                'coin2': {
                    'current_price': 45000 if coin2 == 'bitcoin' else 3200 if coin2 == 'ethereum' else 0.12,
                    'price_change_24h': random.uniform(-5, 5),
                    'price_change_7d': random.uniform(-10, 10),
                    'market_cap': 1000000000000 if coin2 == 'bitcoin' else 400000000000 if coin2 == 'ethereum' else 20000000000,
                    'volume_24h': 50000000000 if coin2 == 'bitcoin' else 20000000000 if coin2 == 'ethereum' else 5000000000
                }
            }

            # Generate dummy chart URL
            chart_url = f"/download/dummy_chart?coin1={coin1}&coin2={coin2}&timeframe={timeframe}"
    except Exception as e:
        print(f"Error in comparison view: {e}")
        # Use dummy data if there's an error
        comparison_data = {
            'coin1': {
                'current_price': 45000 if coin1 == 'bitcoin' else 3200 if coin1 == 'ethereum' else 0.12,
                'price_change_24h': random.uniform(-5, 5),
                'price_change_7d': random.uniform(-10, 10),
                'market_cap': 1000000000000 if coin1 == 'bitcoin' else 400000000000 if coin1 == 'ethereum' else 20000000000,
                'volume_24h': 50000000000 if coin1 == 'bitcoin' else 20000000000 if coin1 == 'ethereum' else 5000000000
            },
            'coin2': {
                'current_price': 45000 if coin2 == 'bitcoin' else 3200 if coin2 == 'ethereum' else 0.12,
                'price_change_24h': random.uniform(-5, 5),
                'price_change_7d': random.uniform(-10, 10),
                'market_cap': 1000000000000 if coin2 == 'bitcoin' else 400000000000 if coin2 == 'ethereum' else 20000000000,
                'volume_24h': 50000000000 if coin2 == 'bitcoin' else 20000000000 if coin2 == 'ethereum' else 5000000000
            }
        }

        # Generate dummy chart URL
        chart_url = f"/download/dummy_chart?coin1={coin1}&coin2={coin2}&timeframe={timeframe}"

    context = {
        'coin1': coin1,
        'coin2': coin2,
        'timeframe': timeframe,
        'coin1_name': coin1_name,
        'coin2_name': coin2_name,
        'coin1_symbol': coin1_symbol,
        'coin2_symbol': coin2_symbol,
        'comparison_data': comparison_data,
        'chart_url': chart_url
    }

    return render(request, 'comparison.html', context)

def calendar(request):
    """
    Calendar page view for viewing Bitcoin calendar events.
    """
    from datetime import datetime, timedelta
    import random

    # Get current date info
    current_date = datetime.now()
    current_month = current_date.month
    current_year = current_date.year

    # Generate sample Bitcoin events for the calendar
    bitcoin_events = {}

    # Add some sample events for the current month
    for day in range(1, 32):
        try:
            event_date = datetime(current_year, current_month, day)
            date_key = event_date.strftime('%Y-%m-%d')

            # Add events for some random days
            if random.random() < 0.3:  # 30% chance of having an event
                price_change = random.uniform(-15, 15)
                base_price = 45000 + random.uniform(-5000, 5000)

                events = []
                alerts = 0

                if abs(price_change) > 10:
                    if price_change > 10:
                        events.append('Significant Price Increase')
                    else:
                        events.append('Significant Price Drop')
                    alerts = random.randint(1, 3)
                elif abs(price_change) > 5:
                    events.append('Notable Price Movement')
                    alerts = random.randint(0, 2)

                if price_change > 8:
                    events.append('Strong Buying Pressure')
                elif price_change < -8:
                    events.append('Market Correction')

                if day == 1:
                    events.append('Monthly Open')
                elif day == 15:
                    events.append('Mid-Month Analysis')

                bitcoin_events[date_key] = {
                    'price': round(base_price, 2),
                    'change': round(price_change, 2),
                    'events': events if events else ['Regular Trading Day'],
                    'alerts': alerts
                }
        except ValueError:
            # Skip invalid dates (like Feb 30)
            continue

    # Get Bitcoin current price for context
    try:
        bitcoin_data = get_current_price('bitcoin')
        current_bitcoin_price = bitcoin_data['usd'] if bitcoin_data else 45000
        current_bitcoin_change = bitcoin_data['usd_24h_change'] if bitcoin_data else 0
    except:
        current_bitcoin_price = 45000
        current_bitcoin_change = 0

    context = {
        'current_month': current_month,
        'current_year': current_year,
        'bitcoin_events': bitcoin_events,
        'current_bitcoin_price': current_bitcoin_price,
        'current_bitcoin_change': current_bitcoin_change,
        'page_title': 'Bitcoin Calendar'
    }

    return render(request, 'calendar.html', context)

def notifications(request):
    """Enhanced notifications page with server-side functionality (no JavaScript)"""
    try:
        # Handle POST requests for notification actions
        if request.method == 'POST':
            action = request.POST.get('action')
            notification_id = request.POST.get('notification_id')

            if action == 'mark_read' and notification_id:
                # Mark notification as read (server-side)
                mark_notification_read_server(notification_id)
                return redirect(request.get_full_path())

            elif action == 'delete' and notification_id:
                # Delete notification (server-side)
                delete_notification_server(notification_id)
                return redirect(request.get_full_path())

            elif action == 'mark_all_read':
                # Mark all notifications as read (server-side)
                mark_all_notifications_read_server()
                return redirect(request.get_full_path())

            elif action == 'delete_all':
                # Delete all notifications (server-side)
                delete_all_notifications_server()
                return redirect(request.get_full_path())

        # Get filter parameters from request
        category_filter = request.GET.get('category', 'all')
        priority_filter = request.GET.get('priority', 'all')
        status_filter = request.GET.get('status', 'all')
        search_query = request.GET.get('search', '')
        page = int(request.GET.get('page', 1))
        per_page = 20

        # Get notifications with server-side filtering
        notifications_data = get_filtered_notifications_server(
            category_filter, priority_filter, status_filter, search_query, page, per_page
        )

        # Calculate statistics
        stats = get_notification_stats_server()

        context = {
            'page_title': 'Notifications Center',
            'current_page': 'notifications',
            'notifications': notifications_data['notifications'],
            'total_pages': notifications_data['total_pages'],
            'current_page_num': page,
            'has_previous': page > 1,
            'has_next': page < notifications_data['total_pages'],
            'previous_page': page - 1 if page > 1 else None,
            'next_page': page + 1 if page < notifications_data['total_pages'] else None,
            'stats': stats,
            'filters': {
                'category': category_filter,
                'priority': priority_filter,
                'status': status_filter,
                'search': search_query
            }
        }

        return render(request, 'notifications.html', context)

    except Exception as e:
        print(f"Error in notifications view: {e}")
        # Return with sample data if there's an error
        sample_notifications = get_sample_notifications_server()
        context = {
            'page_title': 'Notifications Center',
            'current_page': 'notifications',
            'notifications': sample_notifications,
            'total_pages': 1,
            'current_page_num': 1,
            'has_previous': False,
            'has_next': False,
            'previous_page': None,
            'next_page': None,
            'stats': {
                'total': len(sample_notifications),
                'unread': len([n for n in sample_notifications if not n.get('read', False)]),
                'high_priority': len([n for n in sample_notifications if n.get('priority') == 'high']),
                'today': len([n for n in sample_notifications if is_today_server(n.get('time', ''))])
            },
            'filters': {
                'category': 'all',
                'priority': 'all',
                'status': 'all',
                'search': ''
            }
        }
        return render(request, 'notifications.html', context)

def notification_api(request):
    """API endpoint for notification management"""
    if request.method == 'GET':
        # Get notifications with filtering
        category = request.GET.get('category', 'all')
        priority = request.GET.get('priority', 'all')
        status = request.GET.get('status', 'all')
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Mock data for demonstration - in real app, this would come from database
        notifications = []
        for i in range(100):  # Generate sample notifications
            notifications.append({
                'id': f'notif_{i}',
                'title': f'Sample Notification {i + 1}',
                'message': f'This is a sample notification message {i + 1}',
                'category': ['alert', 'sms', 'system', 'price', 'general'][i % 5],
                'priority': ['high', 'medium', 'low'][i % 3],
                'read': i % 3 == 0,
                'time': (datetime.now() - timedelta(minutes=i * 5)).isoformat(),
                'icon': ['bitcoin', 'sms', 'bell', 'chart-line', 'exclamation-triangle'][i % 5]
            })

        # Apply search filter
        if search:
            notifications = [n for n in notifications if search.lower() in n['title'].lower() or search.lower() in n['message'].lower()]

        # Apply filters
        if category != 'all':
            notifications = [n for n in notifications if n['category'] == category]
        if priority != 'all':
            notifications = [n for n in notifications if n['priority'] == priority]
        if status == 'unread':
            notifications = [n for n in notifications if not n['read']]
        elif status == 'read':
            notifications = [n for n in notifications if n['read']]

        # Pagination
        start = (page - 1) * per_page
        end = start + per_page
        paginated_notifications = notifications[start:end]

        return JsonResponse({
            'notifications': paginated_notifications,
            'total': len(notifications),
            'page': page,
            'per_page': per_page,
            'total_pages': (len(notifications) + per_page - 1) // per_page
        })

    elif request.method == 'POST':
        # Create new notification
        data = json.loads(request.body)
        # In real app, save to database
        return JsonResponse({'success': True, 'id': f'notif_{datetime.now().timestamp()}'})

    elif request.method == 'PUT':
        # Update notification
        data = json.loads(request.body)
        # In real app, update in database
        return JsonResponse({'success': True})

    elif request.method == 'DELETE':
        # Delete notification
        notification_id = request.GET.get('id')
        # In real app, delete from database
        return JsonResponse({'success': True})

    return JsonResponse({'error': 'Method not allowed'}, status=405)

def notification_settings(request):
    """API endpoint for notification settings"""
    if request.method == 'GET':
        # Get current settings
        settings = {
            'email_notifications': True,
            'sms_notifications': True,
            'browser_notifications': True,
            'sound_notifications': True,
            'notification_frequency': 'immediate',
            'quiet_hours_enabled': False,
            'quiet_hours_start': '22:00',
            'quiet_hours_end': '08:00',
            'categories': {
                'alert': True,
                'sms': True,
                'system': True,
                'price': True,
                'general': True
            },
            'priorities': {
                'high': True,
                'medium': True,
                'low': True
            }
        }
        return JsonResponse(settings)

    elif request.method == 'POST':
        # Update settings
        data = json.loads(request.body)
        # In real app, save to database
        return JsonResponse({'success': True})

    return JsonResponse({'error': 'Method not allowed'}, status=405)


# Server-side notification management functions (replacing JavaScript functionality)

def get_sample_notifications_server():
    """Generate sample notifications for server-side rendering"""
    sample_notifications = []
    now = datetime.now()

    # Create sample notifications
    notifications_data = [
        {
            'title': 'Bitcoin Price Alert',
            'message': 'Bitcoin has reached $45,000 - your alert threshold!',
            'category': 'alert',
            'priority': 'high',
            'icon': 'bitcoin',
            'time_offset': 30  # 30 minutes ago
        },
        {
            'title': 'SMS Notification Sent',
            'message': 'Alert sent to +1234567890: Bitcoin price alert triggered',
            'category': 'sms',
            'priority': 'medium',
            'icon': 'sms',
            'time_offset': 120  # 2 hours ago
        },
        {
            'title': 'System Update',
            'message': 'Bitcoin Alerter has been updated with new features',
            'category': 'system',
            'priority': 'low',
            'icon': 'cog',
            'time_offset': 1440  # 1 day ago
        },
        {
            'title': 'Ethereum Price Movement',
            'message': 'Ethereum price has increased by 5% in the last hour',
            'category': 'price',
            'priority': 'medium',
            'icon': 'ethereum',
            'time_offset': 60  # 1 hour ago
        },
        {
            'title': 'Weekly Report Available',
            'message': 'Your weekly cryptocurrency portfolio report is ready',
            'category': 'general',
            'priority': 'low',
            'icon': 'chart-bar',
            'time_offset': 10080  # 1 week ago
        }
    ]

    for i, notif_data in enumerate(notifications_data):
        notification = {
            'id': f'sample_{i}',
            'title': notif_data['title'],
            'message': notif_data['message'],
            'category': notif_data['category'],
            'priority': notif_data['priority'],
            'read': i % 3 == 0,  # Some read, some unread
            'time': (now - timedelta(minutes=notif_data['time_offset'])).isoformat(),
            'icon': notif_data['icon'],
            'time_ago': format_time_ago(now - timedelta(minutes=notif_data['time_offset']))
        }
        sample_notifications.append(notification)

    return sample_notifications


def get_filtered_notifications_server(category_filter, priority_filter, status_filter, search_query, page, per_page):
    """Get filtered notifications with server-side filtering"""
    # Start with sample notifications
    all_notifications = get_sample_notifications_server()

    # Apply filters
    filtered_notifications = all_notifications

    if category_filter != 'all':
        filtered_notifications = [n for n in filtered_notifications if n['category'] == category_filter]

    if priority_filter != 'all':
        filtered_notifications = [n for n in filtered_notifications if n['priority'] == priority_filter]

    if status_filter == 'read':
        filtered_notifications = [n for n in filtered_notifications if n['read']]
    elif status_filter == 'unread':
        filtered_notifications = [n for n in filtered_notifications if not n['read']]

    if search_query:
        search_lower = search_query.lower()
        filtered_notifications = [
            n for n in filtered_notifications
            if search_lower in n['title'].lower() or search_lower in n['message'].lower()
        ]

    # Pagination
    total_notifications = len(filtered_notifications)
    total_pages = (total_notifications + per_page - 1) // per_page
    start = (page - 1) * per_page
    end = start + per_page
    paginated_notifications = filtered_notifications[start:end]

    return {
        'notifications': paginated_notifications,
        'total': total_notifications,
        'total_pages': total_pages
    }


def get_notification_stats_server():
    """Calculate notification statistics for server-side rendering"""
    all_notifications = get_sample_notifications_server()

    total = len(all_notifications)
    unread = len([n for n in all_notifications if not n['read']])
    high_priority = len([n for n in all_notifications if n['priority'] == 'high'])

    # Calculate today's notifications
    today = datetime.now().date()
    today_count = 0
    for n in all_notifications:
        notif_date = datetime.fromisoformat(n['time'].replace('Z', '+00:00')).date()
        if notif_date == today:
            today_count += 1

    return {
        'total': total,
        'unread': unread,
        'high_priority': high_priority,
        'today': today_count
    }


def format_time_ago(dt):
    """Format datetime as 'time ago' string"""
    now = datetime.now()
    if dt.tzinfo is not None:
        # Make now timezone-aware if dt is timezone-aware
        from datetime import timezone
        now = now.replace(tzinfo=timezone.utc)

    diff = now - dt

    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    else:
        return "Just now"


def is_today_server(time_str):
    """Check if a time string represents today"""
    try:
        if not time_str:
            return False
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        return dt.date() == datetime.now().date()
    except:
        return False


def mark_notification_read_server(notification_id):
    """Mark a notification as read (server-side implementation)"""
    # In a real application, this would update the database
    # For now, we'll just log the action
    print(f"Marking notification {notification_id} as read")
    return True


def delete_notification_server(notification_id):
    """Delete a notification (server-side implementation)"""
    # In a real application, this would delete from the database
    # For now, we'll just log the action
    print(f"Deleting notification {notification_id}")
    return True


def mark_all_notifications_read_server():
    """Mark all notifications as read (server-side implementation)"""
    # In a real application, this would update all notifications in the database
    # For now, we'll just log the action
    print("Marking all notifications as read")
    return True


def delete_all_notifications_server():
    """Delete all notifications (server-side implementation)"""
    # In a real application, this would delete all notifications from the database
    # For now, we'll just log the action
    print("Deleting all notifications")
    return True