{% extends 'base.html' %}

{% block title %}Notifications Center - Bitcoin Currency Alerter{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Notifications Page Styles */
.notifications-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    padding: 2rem 0;
}

.notifications-header {
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.1), rgba(255, 193, 7, 0.05));
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(247, 147, 26, 0.2);
    backdrop-filter: blur(10px);
}

.notifications-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.notifications-title-section {
    flex: 1;
}

.notifications-title {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #f7931a, #ffc107);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notifications-title i {
    color: #f7931a;
    animation: bellRing 2s infinite;
}

@keyframes bellRing {
    0%, 50%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20%, 40% { transform: rotate(10deg); }
}

.notifications-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
    margin: 0;
}

.notifications-header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary, .btn-danger {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #f7931a, #ffc107);
    color: #1a202c;
    box-shadow: 0 4px 15px rgba(247, 147, 26, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(247, 147, 26, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}
</style>
{% endblock %}

{% block content %}
<div class="notifications-page">
    <!-- Enhanced Page Header -->
    <div class="notifications-header">
        <div class="notifications-header-content">
            <div class="notifications-title-section">
                <h1 class="notifications-title">
                    <i class="fas fa-bell"></i>
                    Notifications Center
                </h1>
                <p class="notifications-subtitle">Manage all your cryptocurrency alerts and notifications in one centralized dashboard</p>
            </div>
            <div class="notifications-header-actions">
                <a href="{% url 'home' %}" class="btn-primary">
                    <i class="fas fa-plus"></i>
                    Create New Alert
                </a>
                <a href="{% url 'charts' %}" class="btn-secondary">
                    <i class="fas fa-chart-line"></i>
                    View Charts
                </a>
                <a href="{% url 'home' %}" class="btn-secondary">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="notifications-stats">
        <div class="stat-card total-notifications">
            <div class="stat-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">Total Notifications</div>
                <div class="stat-trend">
                    <i class="fas fa-chart-line"></i>
                    <span>All time</span>
                </div>
            </div>
            <div class="stat-glow"></div>
        </div>

        <div class="stat-card unread-notifications">
            <div class="stat-icon unread">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ stats.unread }}</div>
                <div class="stat-label">Unread Messages</div>
                <div class="stat-trend">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Requires attention</span>
                </div>
            </div>
            <div class="stat-glow unread-glow"></div>
        </div>

        <div class="stat-card priority-notifications">
            <div class="stat-icon high-priority">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ stats.high_priority }}</div>
                <div class="stat-label">High Priority</div>
                <div class="stat-trend">
                    <i class="fas fa-fire"></i>
                    <span>Critical alerts</span>
                </div>
            </div>
            <div class="stat-glow priority-glow"></div>
        </div>

        <div class="stat-card today-notifications">
            <div class="stat-icon today">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ stats.today }}</div>
                <div class="stat-label">Today's Activity</div>
                <div class="stat-trend">
                    <i class="fas fa-clock"></i>
                    <span>Last 24 hours</span>
                </div>
            </div>
            <div class="stat-glow today-glow"></div>
        </div>
    </div>

<style>
/* Enhanced Statistics Cards */
.notifications-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f7931a, #ffc107);
}

.stat-card.unread-notifications::before {
    background: linear-gradient(90deg, #dc3545, #ff6b6b);
}

.stat-card.priority-notifications::before {
    background: linear-gradient(90deg, #ff4757, #ff3838);
}

.stat-card.today-notifications::before {
    background: linear-gradient(90deg, #2ed573, #7bed9f);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #f7931a, #ffc107);
    color: #1a202c;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(247, 147, 26, 0.3);
}

.stat-icon.unread {
    background: linear-gradient(135deg, #dc3545, #ff6b6b);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.stat-icon.high-priority {
    background: linear-gradient(135deg, #ff4757, #ff3838);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
    animation: priorityPulse 2s infinite;
}

.stat-icon.today {
    background: linear-gradient(135deg, #2ed573, #7bed9f);
    color: white;
    box-shadow: 0 4px 15px rgba(46, 213, 115, 0.3);
}

@keyframes priorityPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.stat-content {
    color: white;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #f7931a, #ffc107);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

.stat-trend i {
    color: #f7931a;
}

.stat-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(247, 147, 26, 0.3), transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-glow {
    opacity: 1;
}

.unread-glow {
    background: radial-gradient(circle, rgba(220, 53, 69, 0.3), transparent);
}

.priority-glow {
    background: radial-gradient(circle, rgba(255, 71, 87, 0.3), transparent);
}

.today-glow {
    background: radial-gradient(circle, rgba(46, 213, 115, 0.3), transparent);
}
</style>

    <!-- Enhanced Filters and Search -->
    <div class="notifications-controls">
        <div class="controls-header">
            <h2 class="controls-title">
                <i class="fas fa-filter"></i>
                Filter & Search Notifications
            </h2>
            <div class="quick-filters">
                <a href="?status=unread" class="quick-filter-btn {% if filters.status == 'unread' %}active{% endif %}">
                    <i class="fas fa-envelope"></i>
                    Unread Only
                </a>
                <a href="?priority=high" class="quick-filter-btn {% if filters.priority == 'high' %}active{% endif %}">
                    <i class="fas fa-exclamation-triangle"></i>
                    High Priority
                </a>
                <a href="?category=alert" class="quick-filter-btn {% if filters.category == 'alert' %}active{% endif %}">
                    <i class="fas fa-bell"></i>
                    Alerts Only
                </a>
            </div>
        </div>

        <form method="GET" class="notifications-filter-form">
            <!-- Enhanced Search Section -->
            <div class="search-section">
                <div class="search-input-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" name="search" value="{{ filters.search }}" placeholder="Search notifications by title, message, or category..." class="search-input">
                    {% if filters.search %}
                    <a href="?category={{ filters.category }}&priority={{ filters.priority }}&status={{ filters.status }}" class="search-clear">
                        <i class="fas fa-times"></i>
                    </a>
                    {% endif %}
                    <button type="submit" class="search-submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- Enhanced Filter Controls -->
            <div class="filter-controls">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-tags"></i>
                            Category
                        </label>
                        <select name="category" class="filter-select" onchange="this.form.submit()">
                            <option value="all" {% if filters.category == 'all' %}selected{% endif %}>🔍 All Categories</option>
                            <option value="alert" {% if filters.category == 'alert' %}selected{% endif %}>🚨 Price Alerts</option>
                            <option value="sms" {% if filters.category == 'sms' %}selected{% endif %}>📱 SMS Notifications</option>
                            <option value="system" {% if filters.category == 'system' %}selected{% endif %}>⚙️ System Updates</option>
                            <option value="price" {% if filters.category == 'price' %}selected{% endif %}>💰 Price Movements</option>
                            <option value="general" {% if filters.category == 'general' %}selected{% endif %}>📋 General Info</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-flag"></i>
                            Priority Level
                        </label>
                        <select name="priority" class="filter-select" onchange="this.form.submit()">
                            <option value="all" {% if filters.priority == 'all' %}selected{% endif %}>🎯 All Priorities</option>
                            <option value="high" {% if filters.priority == 'high' %}selected{% endif %}>🔴 High Priority</option>
                            <option value="medium" {% if filters.priority == 'medium' %}selected{% endif %}>🟡 Medium Priority</option>
                            <option value="low" {% if filters.priority == 'low' %}selected{% endif %}>🟢 Low Priority</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">
                            <i class="fas fa-eye"></i>
                            Read Status
                        </label>
                        <select name="status" class="filter-select" onchange="this.form.submit()">
                            <option value="all" {% if filters.status == 'all' %}selected{% endif %}>👁️ All Messages</option>
                            <option value="unread" {% if filters.status == 'unread' %}selected{% endif %}>✉️ Unread Only</option>
                            <option value="read" {% if filters.status == 'read' %}selected{% endif %}>✅ Read Only</option>
                        </select>
                    </div>
                </div>

                <div class="filter-actions">
                    <button type="submit" class="btn-primary filter-apply">
                        <i class="fas fa-filter"></i>
                        Apply Filters
                    </button>
                    <a href="?" class="btn-secondary filter-clear">
                        <i class="fas fa-refresh"></i>
                        Reset All
                    </a>
                    <button type="button" class="btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </form>
    </div>

<style>
/* Enhanced Controls Styling */
.notifications-controls {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.controls-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.controls-title i {
    color: #f7931a;
}

.quick-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-filter-btn:hover,
.quick-filter-btn.active {
    background: linear-gradient(135deg, #f7931a, #ffc107);
    color: #1a202c;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(247, 147, 26, 0.3);
}

.search-section {
    margin-bottom: 1.5rem;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-input-container:focus-within {
    border-color: #f7931a;
    box-shadow: 0 0 20px rgba(247, 147, 26, 0.3);
}

.search-icon {
    padding: 1rem;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1.1rem;
}

.search-input {
    flex: 1;
    padding: 1rem 0;
    background: transparent;
    border: none;
    color: white;
    font-size: 1rem;
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-clear {
    padding: 1rem;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: color 0.3s ease;
}

.search-clear:hover {
    color: #dc3545;
}

.search-submit {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f7931a, #ffc107);
    color: #1a202c;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-submit:hover {
    background: linear-gradient(135deg, #ffc107, #f7931a);
}

.filter-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.filter-label i {
    color: #f7931a;
}

.filter-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    border-color: #f7931a;
    box-shadow: 0 0 15px rgba(247, 147, 26, 0.3);
    outline: none;
}

.filter-select option {
    background: #2d3748;
    color: white;
    padding: 0.5rem;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.filter-apply,
.filter-clear {
    min-width: 140px;
}

@media (max-width: 768px) {
    .controls-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        flex-direction: column;
    }

    .quick-filters {
        justify-content: center;
    }
}
</style>

    <!-- Notifications List -->
    <div class="notifications-content">
        <div class="notifications-list-header">
            <div class="list-actions">
                <form method="POST" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="mark_all_read">
                    <button type="submit" class="btn-secondary">
                        <i class="fas fa-check"></i>
                        Mark All Read
                    </button>
                </form>
                <form method="POST" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="delete_all">
                    <button type="submit" class="btn-danger" onclick="return confirm('Are you sure you want to delete all notifications?')">
                        <i class="fas fa-trash"></i>
                        Delete All
                    </button>
                </form>
            </div>
        </div>

        {% if notifications %}
            <!-- Notifications List Container -->
            <div class="notifications-list">
                {% for notification in notifications %}
                <div class="notification-item {% if not notification.read %}unread{% else %}read{% endif %} priority-{{ notification.priority }}">
                    <div class="notification-priority-indicator {{ notification.priority }}"></div>
                    <div class="notification-icon {{ notification.icon }}">
                        <i class="{% if notification.icon == 'bitcoin' or notification.icon == 'ethereum' %}fab{% elif notification.icon == 'newspaper' %}far{% else %}fas{% endif %} fa-{{ notification.icon }}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-header">
                            <div class="notification-title">{{ notification.title }}</div>
                            <div class="notification-category-badge">{{ notification.category }}</div>
                        </div>
                        <div class="notification-message">{{ notification.message }}</div>
                        <div class="notification-meta">
                            <div class="notification-time-full">{{ notification.time_ago }}</div>
                            <div class="notification-priority-badge priority-{{ notification.priority }}">
                                {{ notification.priority|upper }}
                            </div>
                        </div>
                    </div>
                    <div class="notification-actions-full">
                        <form method="POST" style="display: inline;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="mark_read">
                            <input type="hidden" name="notification_id" value="{{ notification.id }}">
                            <button type="submit" class="notification-action-btn" title="Mark as {% if notification.read %}unread{% else %}read{% endif %}">
                                <i class="fas fa-{% if notification.read %}envelope{% else %}check{% endif %}"></i>
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="notification_id" value="{{ notification.id }}">
                            <button type="submit" class="notification-action-btn" title="Delete" onclick="return confirm('Are you sure you want to delete this notification?')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if total_pages > 1 %}
            <div class="notifications-pagination">
                {% if has_previous %}
                <a href="?page={{ previous_page }}&category={{ filters.category }}&priority={{ filters.priority }}&status={{ filters.status }}&search={{ filters.search }}" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </a>
                {% else %}
                <button class="pagination-btn" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                {% endif %}

                <div class="pagination-info">
                    <span>Page {{ current_page_num }} of {{ total_pages }}</span>
                </div>

                {% if has_next %}
                <a href="?page={{ next_page }}&category={{ filters.category }}&priority={{ filters.priority }}&status={{ filters.status }}&search={{ filters.search }}" class="pagination-btn">
                    Next
                    <i class="fas fa-chevron-right"></i>
                </a>
                {% else %}
                <button class="pagination-btn" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
                {% endif %}
            </div>
            {% endif %}
        {% else %}
            <!-- Empty State -->
            <div class="notifications-empty">
                <div class="empty-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <h3>No notifications found</h3>
                <p>You don't have any notifications matching your current filters.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Notification Settings Modal -->
<div class="modal" id="settingsModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Notification Settings</h2>
            <button class="modal-close" id="closeSettingsModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="settings-section">
                <h3>Notification Types</h3>
                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" id="emailNotifications" checked>
                        <span class="checkmark"></span>
                        Email Notifications
                    </label>
                </div>
                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" id="smsNotifications" checked>
                        <span class="checkmark"></span>
                        SMS Notifications
                    </label>
                </div>
                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" id="browserNotifications" checked>
                        <span class="checkmark"></span>
                        Browser Notifications
                    </label>
                </div>
                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" id="soundNotifications" checked>
                        <span class="checkmark"></span>
                        Sound Notifications
                    </label>
                </div>
            </div>

            <div class="settings-section">
                <h3>Frequency</h3>
                <div class="setting-item">
                    <label>Notification Frequency:</label>
                    <select id="notificationFrequency" class="setting-select">
                        <option value="immediate">Immediate</option>
                        <option value="hourly">Hourly Digest</option>
                        <option value="daily">Daily Digest</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3>Quiet Hours</h3>
                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" id="quietHoursEnabled">
                        <span class="checkmark"></span>
                        Enable Quiet Hours
                    </label>
                </div>
                <div class="setting-item">
                    <label>From:</label>
                    <input type="time" id="quietHoursStart" value="22:00" class="setting-input">
                </div>
                <div class="setting-item">
                    <label>To:</label>
                    <input type="time" id="quietHoursEnd" value="08:00" class="setting-input">
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" id="cancelSettings">Cancel</button>
            <button class="btn-primary" id="saveSettings">Save Settings</button>
        </div>
    </div>
</div>

<!-- Create Notification Modal -->
<div class="modal" id="createNotificationModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Create Test Notification</h2>
            <button class="modal-close" id="closeCreateModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>Title:</label>
                <input type="text" id="notificationTitle" class="form-input" placeholder="Enter notification title">
            </div>
            <div class="form-group">
                <label>Message:</label>
                <textarea id="notificationMessage" class="form-textarea" placeholder="Enter notification message"></textarea>
            </div>
            <div class="form-group">
                <label>Category:</label>
                <select id="notificationCategory" class="form-select">
                    <option value="general">General</option>
                    <option value="alert">Alert</option>
                    <option value="sms">SMS</option>
                    <option value="system">System</option>
                    <option value="price">Price</option>
                </select>
            </div>
            <div class="form-group">
                <label>Priority:</label>
                <select id="notificationPriority" class="form-select">
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                </select>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" id="cancelCreate">Cancel</button>
            <button class="btn-primary" id="createNotification">Create Notification</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- JavaScript functionality replaced with server-side Python/Django -->
{% endblock %}
